"""
IAM 服务依赖注入模块配置

统一管理需要进行依赖注入的模块列表，避免在多个文件中重复维护
"""

from tasks.scheduler import dispatch_scheduler

# 路由模块列表
ROUTE_MODULES = [
    "routes.tenants",
    "routes.users",
    "routes.auth",
    "routes.rbac",
    "routes.roles",
    "routes.permissions",
    "routes.audit",
    "routes.advanced_security",
    "routes.system",
    "routes.system_config",
]

# 任务模块列表（从调度器获取）
TASK_MODULES = dispatch_scheduler()

# 完整的依赖注入模块列表
WIRE_MODULES = ROUTE_MODULES + TASK_MODULES

__all__ = ["WIRE_MODULES", "ROUTE_MODULES", "TASK_MODULES"]
