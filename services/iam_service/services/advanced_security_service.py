"""
高级安全服务

提供高级安全功能，包括MFA、安全策略、威胁检测等
"""

import base64
import io
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type

import pyotp
import qrcode
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from commonlib.exceptions.exceptions import (
    AuthenticationError,
    BusinessError,
    NotFoundError,
    ValidationError,
)
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    AuditLog,
    AuditLogBuilder,
    SecurityEvent,
    SecurityPolicy,
    Tenant,
    User,
    UserMFA,
)


class AdvancedSecurityService:
    """高级安全服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        user_mfa_model: Type[UserMFA],
        security_policy_model: Type[SecurityPolicy],
        security_event_model: Type[SecurityEvent],
        audit_log_model: Type[AuditLog],
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_repo = redis_repo

        # 业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.user_mfa_model = user_mfa_model
        self.security_policy_model = security_policy_model
        self.security_event_model = security_event_model
        self.audit_log_model = audit_log_model

        # 安全工具方法将在类内部实现

        # 缓存键前缀
        self.mfa_setup_prefix = "mfa_setup:"
        self.backup_codes_prefix = "backup_codes:"
        self.security_policy_prefix = "security_policy:"

        # MFA类型
        self.mfa_types = ["totp", "sms", "email"]

        # 安全事件类型
        self.event_types = [
            "login_anomaly",
            "brute_force",
            "privilege_escalation",
            "data_access",
            "mfa_bypass",
            "suspicious_activity",
        ]

        # 严重程度
        self.severities = ["low", "medium", "high", "critical"]

        # 事件状态
        self.event_statuses = ["pending", "investigating", "resolved", "false_positive"]

    # ===== MFA管理 =====

    async def setup_mfa(
        self,
        tenant_id: str,
        user_id: str,
        mfa_type: str,
        phone: Optional[str] = None,
        email: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        设置MFA

        为用户设置多因子认证
        """
        try:
            # 验证MFA类型
            if mfa_type not in self.mfa_types:
                raise ValidationError(f"不支持的MFA类型: {mfa_type}")

            # 验证用户存在性
            user = await self._get_user_by_id(user_id)
            if not user or user.tenant_id != tenant_id:
                raise NotFoundError("用户不存在")

            # 验证联系方式
            if mfa_type == "sms" and not phone:
                raise ValidationError("SMS MFA需要提供手机号")
            if mfa_type == "email" and not email:
                raise ValidationError("Email MFA需要提供邮箱")

            # 生成设置令牌
            setup_token = f"mfa_setup_{uuid.uuid4()}"

            # 生成MFA密钥和配置
            mfa_config = {}
            qr_code_url = None
            secret_key = None

            if mfa_type == "totp":
                # 生成TOTP密钥
                secret_key = self._generate_totp_secret()
                mfa_config["secret"] = secret_key

                # 生成二维码URL
                qr_code_url = self._generate_totp_qr_url(
                    secret_key, user.email or user.username, f"{tenant_id} IAM"
                )
            elif mfa_type == "sms":
                mfa_config["phone"] = phone
            elif mfa_type == "email":
                mfa_config["email"] = email

            # 生成备用恢复码
            backup_codes = [self._generate_backup_code() for _ in range(10)]

            # 存储设置信息到缓存
            setup_info = {
                "user_id": user_id,
                "tenant_id": tenant_id,
                "mfa_type": mfa_type,
                "mfa_config": mfa_config,
                "backup_codes": backup_codes,
                "created_at": datetime.now().isoformat(),
            }

            await self.redis_repo.set(
                f"{self.mfa_setup_prefix}{setup_token}",
                setup_info,
                ttl=1800,  # 30分钟过期
            )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="SETUP_MFA",
                resource_type="USER_MFA",
                details={"mfa_type": mfa_type, "setup_token": setup_token},
            )

            return {
                "setup_token": setup_token,
                "qr_code_url": qr_code_url,
                "secret_key": secret_key,
                "backup_codes": backup_codes,
                "expires_at": (datetime.now() + timedelta(minutes=30)).isoformat(),
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise
            raise BusinessError(f"设置MFA失败: {str(e)}")

    async def verify_mfa_setup(
        self, tenant_id: str, user_id: str, setup_token: str, verification_code: str
    ) -> Dict[str, Any]:
        """
        验证MFA设置

        验证MFA设置并激活
        """
        try:
            # 获取设置信息
            setup_info = await self.redis_repo.get(f"{self.mfa_setup_prefix}{setup_token}")
            if not setup_info:
                raise NotFoundError("设置令牌不存在或已过期")

            # 验证用户匹配
            if setup_info["user_id"] != user_id or setup_info["tenant_id"] != tenant_id:
                raise ValidationError("设置令牌与用户不匹配")

            # 验证验证码
            mfa_type = setup_info["mfa_type"]
            mfa_config = setup_info["mfa_config"]

            if mfa_type == "totp":
                # 验证TOTP验证码
                if not self._verify_totp(mfa_config["secret"], verification_code):
                    raise AuthenticationError("TOTP验证码错误")
            elif mfa_type in ["sms", "email"]:
                # TODO: 实现SMS/Email验证码验证
                # 这里暂时简单验证
                if verification_code != "123456":  # 临时验证码
                    raise AuthenticationError("验证码错误")

            # 检查是否已存在MFA配置
            existing_mfa = await self._get_user_mfa(user_id)
            if existing_mfa:
                # 更新现有配置
                existing_mfa.mfa_type = mfa_type
                if mfa_type == "totp":
                    existing_mfa.secret_key = mfa_config["secret"]
                existing_mfa.backup_codes = setup_info["backup_codes"]
                existing_mfa.is_enabled = True
                existing_mfa.verified_at = datetime.now()
                existing_mfa.updated_at = datetime.now()
                mfa_id = existing_mfa.id
            else:
                # 创建新的MFA配置
                user_mfa = self.user_mfa_model(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    mfa_type=mfa_type,
                    secret_key=mfa_config.get("secret") if mfa_type == "totp" else None,
                    backup_codes=setup_info["backup_codes"],
                    is_enabled=True,
                    verified_at=datetime.now(),
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                self.session.add(user_mfa)
                await self.session.flush()
                mfa_id = user_mfa.id

            # 存储备用恢复码
            await self._store_backup_codes(user_id, setup_info["backup_codes"])

            # 更新用户MFA状态
            user = await self._get_user_by_id(user_id)
            user.mfa_enabled = True
            user.updated_at = datetime.now()

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="ENABLE_MFA",
                resource_type="USER_MFA",
                details={"mfa_type": mfa_type, "mfa_id": mfa_id},
            )

            await self.session.commit()

            # 清除设置令牌
            await self.redis_repo.delete(f"{self.mfa_setup_prefix}{setup_token}")

            # 创建安全事件
            await self._create_security_event(
                tenant_id=tenant_id,
                event_type="mfa_enabled",
                severity="low",
                title="用户启用MFA",
                description=f"用户 {user.username} 启用了 {mfa_type.upper()} 多因子认证",
                user_id=user_id,
            )

            return {
                "user_id": user_id,
                "mfa_enabled": True,
                "mfa_type": mfa_type,
                "enabled_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError, AuthenticationError)):
                raise
            raise BusinessError(f"验证MFA设置失败: {str(e)}")

    async def disable_mfa(
        self,
        tenant_id: str,
        user_id: str,
        verification_code: str,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        禁用MFA

        禁用用户的多因子认证
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id)
            if not user or user.tenant_id != tenant_id:
                raise NotFoundError("用户不存在")

            if not user.mfa_enabled:
                raise ValidationError("用户未启用MFA")

            # 获取MFA配置
            user_mfa = await self._get_user_mfa(user_id)
            if not user_mfa:
                raise NotFoundError("MFA配置不存在")

            # 验证当前MFA验证码
            if user_mfa.mfa_type == "totp":
                secret = user_mfa.secret_key
                if not self._verify_totp(secret, verification_code):
                    raise AuthenticationError("MFA验证码错误")
            else:
                # TODO: 实现其他MFA类型的验证
                if verification_code != "123456":  # 临时验证码
                    raise AuthenticationError("验证码错误")

            # 禁用MFA
            user_mfa.is_enabled = False
            user_mfa.updated_at = datetime.now()
            # 注意：UserMFA模型中没有disabled_at和disabled_reason字段，可以使用backup_codes字段存储禁用信息
            if user_mfa.backup_codes is None:
                user_mfa.backup_codes = {}
            user_mfa.backup_codes["disabled_at"] = datetime.now().isoformat()
            user_mfa.backup_codes["disabled_reason"] = reason

            # 更新用户MFA状态
            user.mfa_enabled = False
            user.updated_at = datetime.now()

            # 清除备用恢复码
            await self._clear_backup_codes(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="DISABLE_MFA",
                resource_type="USER_MFA",
                details={"mfa_type": user_mfa.mfa_type, "reason": reason},
            )

            await self.session.commit()

            # 创建安全事件
            await self._create_security_event(
                tenant_id=tenant_id,
                event_type="mfa_disabled",
                severity="medium",
                title="用户禁用MFA",
                description=f"用户 {user.username} 禁用了多因子认证",
                user_id=user_id,
                details={"reason": reason},
            )

            return {
                "user_id": user_id,
                "mfa_enabled": False,
                "disabled_at": datetime.now().isoformat(),
                "reason": reason,
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError, AuthenticationError)):
                raise
            raise BusinessError(f"禁用MFA失败: {str(e)}")

    async def generate_backup_codes(
        self, tenant_id: str, user_id: str, count: int = 10
    ) -> Dict[str, Any]:
        """
        生成备用恢复码

        为用户生成MFA备用恢复码
        """
        try:
            # 验证用户存在性和MFA状态
            user = await self._get_user_by_id(user_id)
            if not user or user.tenant_id != tenant_id:
                raise NotFoundError("用户不存在")

            if not user.mfa_enabled:
                raise ValidationError("用户未启用MFA")

            # 生成新的备用恢复码
            backup_codes = [self._generate_backup_code() for _ in range(count)]

            # 存储备用恢复码
            await self._store_backup_codes(user_id, backup_codes)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="GENERATE_BACKUP_CODES",
                resource_type="USER_MFA",
                details={"count": count},
            )

            return {
                "user_id": user_id,
                "backup_codes": backup_codes,
                "generated_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(days=365)).isoformat(),
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise
            raise BusinessError(f"生成备用恢复码失败: {str(e)}")

    # ===== 安全策略管理 =====

    async def set_security_policy(
        self,
        tenant_id: Optional[str],
        policy_name: str,
        policy_config: Dict[str, Any],
        enabled: bool = True,
    ) -> Dict[str, Any]:
        """
        设置安全策略

        设置租户或全局安全策略
        """
        try:
            # 验证租户（如果指定）
            if tenant_id:
                tenant = await self._get_tenant_by_id(tenant_id)
                if not tenant:
                    raise NotFoundError("租户不存在")

            # 验证策略配置
            self._validate_policy_config(policy_name, policy_config)

            # 查找现有策略
            existing_policy = await self._get_security_policy(tenant_id, policy_name)

            if existing_policy:
                # 更新现有策略
                existing_policy.policy_config = policy_config
                existing_policy.enabled = enabled
                existing_policy.updated_at = datetime.now()
                policy_id = existing_policy.policy_id
            else:
                # 创建新策略
                policy_id = f"policy_{uuid.uuid4()}"
                security_policy = self.security_policy_model(
                    policy_id=policy_id,
                    tenant_id=tenant_id,
                    policy_name=policy_name,
                    policy_config=policy_config,
                    enabled=enabled,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                self.session.add(security_policy)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="SET_SECURITY_POLICY",
                resource_type="SECURITY_POLICY",
                details={"policy_name": policy_name, "enabled": enabled},
            )

            await self.session.commit()

            # 清除策略缓存
            await self._clear_policy_cache(tenant_id, policy_name)

            return {
                "policy_id": policy_id,
                "policy_name": policy_name,
                "policy_config": policy_config,
                "enabled": enabled,
                "updated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError)):
                raise
            raise BusinessError(f"设置安全策略失败: {str(e)}")

    # ===== 安全事件管理 =====

    async def query_security_events(
        self,
        tenant_id: str,
        event_type: Optional[str] = None,
        severity: Optional[str] = None,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        status: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
    ) -> Dict[str, Any]:
        """
        查询安全事件

        查询安全事件和威胁检测结果
        """
        try:
            # 构建查询条件
            conditions = [self.security_event_model.tenant_id == tenant_id]

            if event_type:
                conditions.append(self.security_event_model.event_type == event_type)

            if severity:
                conditions.append(self.security_event_model.severity == severity)

            if start_time:
                start_dt = datetime.fromisoformat(start_time)
                conditions.append(self.security_event_model.created_at >= start_dt)

            if end_time:
                end_dt = datetime.fromisoformat(end_time)
                conditions.append(self.security_event_model.created_at <= end_dt)

            if user_id:
                conditions.append(self.security_event_model.user_id == user_id)

            if ip_address:
                conditions.append(self.security_event_model.ip_address == ip_address)

            if status:
                conditions.append(self.security_event_model.status == status)

            # 查询总数
            count_stmt = select(func.count(self.security_event_model.event_id)).where(
                and_(*conditions)
            )
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            stmt = (
                select(self.security_event_model)
                .where(and_(*conditions))
                .order_by(desc(self.security_event_model.created_at))
                .offset(offset)
                .limit(page_size)
            )
            result = await self.session.execute(stmt)
            events = result.scalars().all()

            # 构建事件列表
            event_list = []
            for event in events:
                # 获取用户名
                username = None
                if event.user_id:
                    user = await self._get_user_by_id(event.user_id)
                    if user:
                        username = user.username

                event_info = {
                    "event_id": event.event_id,
                    "tenant_id": event.tenant_id,
                    "event_type": event.event_type,
                    "severity": event.severity,
                    "title": event.title,
                    "description": event.description,
                    "user_id": event.user_id,
                    "username": username,
                    "ip_address": event.ip_address,
                    "user_agent": event.user_agent,
                    "details": event.details,
                    "status": event.status,
                    "assigned_to": event.assigned_to,
                    "notes": event.notes,
                    "created_at": event.created_at.isoformat(),
                    "updated_at": (event.updated_at.isoformat() if event.updated_at else None),
                }
                event_list.append(event_info)

            return {
                "events": event_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "has_next": offset + page_size < total,
            }

        except Exception as e:
            raise BusinessError(f"查询安全事件失败: {str(e)}")

    async def update_security_event(
        self,
        tenant_id: str,
        event_id: str,
        status: str,
        notes: Optional[str] = None,
        assigned_to: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        更新安全事件

        更新安全事件的处理状态
        """
        try:
            # 验证状态
            if status not in self.event_statuses:
                raise ValidationError(f"无效的事件状态: {status}")

            # 获取安全事件
            event = await self._get_security_event(event_id)
            if not event or event.tenant_id != tenant_id:
                raise NotFoundError("安全事件不存在")

            # 更新事件
            event.status = status
            event.notes = notes
            event.assigned_to = assigned_to
            event.updated_at = datetime.now()

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=assigned_to,
                action="UPDATE_SECURITY_EVENT",
                resource_type="SECURITY_EVENT",
                details={
                    "event_id": event_id,
                    "status": status,
                    "assigned_to": assigned_to,
                },
            )

            await self.session.commit()

            return {
                "event_id": event_id,
                "status": status,
                "updated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            if isinstance(e, (ValidationError, NotFoundError)):
                raise
            raise BusinessError(f"更新安全事件失败: {str(e)}")

    # ===== 辅助方法 =====

    async def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(self.user_model.user_id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_mfa(self, user_id: str):
        """获取用户MFA配置"""
        stmt = select(self.user_mfa_model).where(self.user_mfa_model.user_id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_security_policy(self, tenant_id: Optional[str], policy_name: str):
        """获取安全策略"""
        stmt = select(self.security_policy_model).where(
            and_(
                self.security_policy_model.tenant_id == tenant_id,
                self.security_policy_model.policy_name == policy_name,
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_security_event(self, event_id: str):
        """获取安全事件"""
        stmt = select(self.security_event_model).where(
            self.security_event_model.event_id == event_id
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _store_backup_codes(self, user_id: str, backup_codes: List[str]):
        """存储备用恢复码"""
        backup_data = {
            "codes": backup_codes,
            "generated_at": datetime.now().isoformat(),
            "used_codes": [],
        }
        await self.redis_repo.set(
            f"{self.backup_codes_prefix}{user_id}", backup_data, ttl=31536000  # 1年过期
        )

    async def _clear_backup_codes(self, user_id: str):
        """清除备用恢复码"""
        await self.redis_repo.delete(f"{self.backup_codes_prefix}{user_id}")

    def _validate_policy_config(self, policy_name: str, policy_config: Dict[str, Any]):
        """验证策略配置"""
        # TODO: 实现策略配置验证
        # 根据不同的策略类型验证配置的有效性

    async def _clear_policy_cache(self, tenant_id: Optional[str], policy_name: str):
        """清除策略缓存"""
        cache_key = f"{self.security_policy_prefix}{tenant_id or 'global'}:{policy_name}"
        await self.redis_repo.delete(cache_key)

    async def _create_security_event(
        self,
        tenant_id: str,
        event_type: str,
        severity: str,
        title: str,
        description: str,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        """创建安全事件"""
        event_id = f"event_{uuid.uuid4()}"
        security_event = self.security_event_model(
            event_id=event_id,
            tenant_id=tenant_id,
            event_type=event_type,
            severity=severity,
            title=title,
            description=description,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            details=details or {},
            status="pending",
            created_at=datetime.now(),
        )
        self.session.add(security_event)
        await self.session.commit()

    async def _create_audit_log(
        self,
        tenant_id: Optional[str],
        user_id: Optional[str],
        action: str,
        resource_type: str,
        details: Dict[str, Any],
    ):
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=details.get("resource_id"),
                details=details,
                result="success",
            )
            self.session.add(audit_log)
            # 注意：不在这里commit，由调用方决定
        except Exception as e:
            # 审计日志失败不应该影响主业务
            print(
                f"审计日志记录失败: {action} - {resource_type} by {user_id} in {tenant_id} - {str(e)}"
            )

    # ===== 安全工具方法 =====

    def _generate_totp_secret(self) -> str:
        """生成TOTP密钥"""
        return pyotp.random_base32()

    def _generate_totp_qr_url(self, secret: str, user_identifier: str, issuer: str) -> str:
        """生成TOTP二维码URL"""
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(name=user_identifier, issuer_name=issuer)

        # 生成二维码
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)

        # 转换为base64图片
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = io.BytesIO()
        img.save(buffer, format="PNG")
        img_str = base64.b64encode(buffer.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    def _verify_totp(self, secret: str, token: str) -> bool:
        """验证TOTP令牌"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=1)  # 允许前后30秒的时间窗口
        except Exception:
            return False

    def _generate_backup_code(self) -> str:
        """生成备用恢复码"""
        import secrets
        import string

        # 生成8位数字和字母组合的恢复码
        alphabet = string.ascii_uppercase + string.digits
        # 排除容易混淆的字符
        alphabet = alphabet.replace("0", "").replace("O", "").replace("1", "").replace("I", "")

        code = "".join(secrets.choice(alphabet) for _ in range(8))
        # 格式化为 XXXX-XXXX 的形式
        return f"{code[:4]}-{code[4:]}"
