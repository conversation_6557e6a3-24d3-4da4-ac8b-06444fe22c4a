"""
用户服务

提供用户管理的业务逻辑实现
支持用户生命周期管理、信息维护、状态管理和安全功能
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Type

from sqlalchemy import and_, func, or_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from commonlib.exceptions.exceptions import (
    BusinessError,
    DatabaseError,
    DuplicateResourceError,
    NotFoundError,
    ValidationError,
)
from commonlib.interface.infra_redis.cache import RedisCacheService
from domain_common.models import CommonStatus
from domain_common.models.iam_models import (
    AuditLog,
    AuditLogBuilder,
    Permission,
    Role,
    RolePermission,
    Tenant,
    User,
    UserRole,
)
from domain_common.security import SecurityUtils


class UserService:
    """用户服务类"""

    def __init__(
        self,
        session: AsyncSession,
        redis_cache_service: RedisCacheService,
        user_model: Type[User],
        tenant_model: Type[Tenant],
        role_model: Type[Role],
        permission_model: Type[Permission],
        user_role_model: Type[UserRole],
        role_permission_model: Type[RolePermission],
        audit_log_model: Type[AuditLog],
        security_utils: SecurityUtils,
    ):
        # 数据库会话和缓存
        self.session = session
        self.redis_cache_service = redis_cache_service

        # 核心业务模型
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.role_model = role_model
        self.permission_model = permission_model

        # 关联关系模型
        self.user_role_model = user_role_model
        self.role_permission_model = role_permission_model

        # 审计模型
        self.audit_log_model = audit_log_model

        # 安全工具
        self.security_utils = security_utils

        # 安全工具
        self.security_utils = SecurityUtils()

        # 用户状态定义
        self.USER_STATUS = {
            "PENDING": "pending",  # 待激活
            "ACTIVE": "active",  # 活跃
            "INACTIVE": "inactive",  # 非活跃
            "LOCKED": "locked",  # 锁定
            "DELETED": "deleted",  # 已删除
        }

        # 状态转换规则
        self.STATUS_TRANSITIONS = {
            "pending": ["active", "locked", "deleted"],
            "active": ["locked", "inactive", "deleted"],
            "locked": ["active", "deleted"],
            "inactive": ["active", "deleted"],
            "deleted": [],  # 已删除状态无法转换
        }

    async def create_user(
        self,
        tenant_id: str,
        username: str,
        email: str,
        password: str,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        status: str = CommonStatus.PENDING,
        profile: Optional[Dict[str, Any]] = None,
        send_welcome_email: bool = True,
    ) -> Dict[str, Any]:
        """
        创建用户

        管理员创建用户账户，包含完整的初始化流程
        """
        try:
            # 验证租户是否存在且处于活跃状态
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            # 验证用户名、邮箱、手机号在租户内的唯一性
            await self._validate_user_uniqueness(tenant_id, username, email, phone)

            # 验证密码是否符合租户密码策略
            await self._validate_password_policy(tenant_id, password)

            # 生成用户ID
            user_id = f"user_{uuid.uuid4()}"

            # 加密密码
            encrypted_password = self.security_utils.hash_password(password)

            # 创建用户记录
            user = self.user_model(
                user_id=user_id,
                tenant_id=tenant_id,
                username=username,
                email=email,
                phone=phone,
                nickname=nickname,
                password_hash=encrypted_password,
                status=status,
                profile=profile or {},
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            self.session.add(user)
            await self.session.flush()  # 获取生成的ID

            # 分配默认角色
            await self._assign_default_roles(user_id, tenant_id)

            # 生成激活令牌（如果状态为pending）
            activation_token = None
            if status == "pending":
                activation_token = await self._generate_activation_token(user_id)

            # 发送欢迎邮件（如果需要）
            if send_welcome_email:
                await self._send_welcome_email(user_id, email, username, activation_token)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # 创建者ID
                action="CREATE_USER",
                resource_type="USER",
                resource_id=user_id,
                details={"username": username, "email": email},
            )

            await self.session.commit()

            # 缓存用户基本信息
            await self._cache_user_info(
                user_id,
                {
                    "user_id": user_id,
                    "username": username,
                    "email": email,
                    "status": status,
                    "tenant_id": tenant_id,
                },
            )

            # 返回创建结果
            return {
                "user_id": user_id,
                "username": username,
                "email": email,
                "phone": phone,
                "nickname": nickname,
                "status": status,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat(),
                "last_login": None,
                "login_count": 0,
                "roles": [],
                "profile": profile,
                "activation_required": status == "pending",
            }

        except IntegrityError as e:
            await self.session.rollback()
            if "username" in str(e):
                raise DuplicateResourceError("用户名已存在", "username", username)
            elif "email" in str(e):
                raise DuplicateResourceError("邮箱已存在", "email", email)
            elif "phone" in str(e):
                raise DuplicateResourceError("手机号已存在", "phone", phone)
            else:
                raise DatabaseError("数据库约束错误")
        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"创建用户失败: {str(e)}")

    async def register_user(
        self,
        tenant_code: str,
        username: str,
        email: str,
        password: str,
        verification_code: str,
        code_id: str,
        phone: Optional[str] = None,
        nickname: Optional[str] = None,
        agree_terms: bool = True,
    ) -> Dict[str, Any]:
        """
        用户注册

        用户自主注册功能，需要验证码验证
        """
        try:
            # 验证服务条款同意
            if not agree_terms:
                raise ValidationError("必须同意服务条款")
            # 根据租户编码获取租户信息
            tenant = await self._get_tenant_by_code(tenant_code)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未开放注册")

            # 验证验证码
            # await self._verify_code(code_id, verification_code, "register")

            # 验证用户名、邮箱、手机号在租户内的唯一性
            # await self._validate_user_uniqueness(tenant.tenant_id, username, email, phone)

            # 验证密码是否符合租户密码策略
            # await self._validate_password_policy(tenant.tenant_id, password)

            # 生成用户ID
            user_id = f"user_{uuid.uuid4()}"

            # 加密密码
            encrypted_password = self.security_utils.hash_password(password)

            # 创建用户记录（待激活状态）
            user = self.user_model(
                user_id=user_id,
                tenant_id=tenant.tenant_id,
                username=username,
                email=email,
                phone=phone,
                nickname=nickname,
                password_hash=encrypted_password,
                status=CommonStatus.PENDING,
                profile={},
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
            self.session.add(user)
            await self.session.flush()

            # 生成激活令牌
            # activation_token = await self._generate_activation_token(user_id)

            # 发送激活邮件
            # await self._send_activation_email(user_id, email, username, activation_token)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant.tenant_id,
                user_id=None,
                action="REGISTER_USER",
                resource_type="USER",
                resource_id=user_id,
                details={"username": username, "email": email},
            )

            await self.session.commit()

            return {
                "user_id": user_id,
                "username": username,
                "email": email,
                "status": CommonStatus.PENDING,
                "activation_required": True,
                "created_at": user.created_at.isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"用户注册失败: {str(e)}")

    async def activate_user(self, activation_token: str) -> Dict[str, Any]:
        """
        激活用户账户

        通过激活令牌激活用户账户
        """
        try:
            # 验证激活令牌
            user_id = await self._verify_activation_token(activation_token)
            if not user_id:
                raise ValidationError("激活令牌无效或已过期")

            # 获取用户信息
            user = await self._get_user_by_id_only(user_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 检查用户状态
            if user.status != CommonStatus.PENDING:
                raise BusinessError("用户已激活或状态异常")

            # 更新用户状态为活跃
            user.status = CommonStatus.ACTIVE
            user.updated_at = datetime.now()

            # 分配默认角色
            await self._assign_default_roles(user_id, user.tenant_id)

            await self.session.commit()

            # 删除激活令牌
            await self._delete_activation_token(activation_token)

            # 缓存用户基本信息
            await self._cache_user_info(
                user_id,
                {
                    "user_id": user_id,
                    "username": user.username,
                    "email": user.email,
                    "status": CommonStatus.ACTIVE,
                    "tenant_id": user.tenant_id,
                },
            )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=user.tenant_id,
                user_id=user_id,
                action="ACTIVATE_USER",
                resource_type="USER",
                resource_id=user_id,
                details={"activation_method": "email_token"},
            )

            return {
                "user_id": user_id,
                "username": user.username,
                "email": user.email,
                "status": CommonStatus.ACTIVE,
                "activated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"激活用户失败: {str(e)}")

    async def list_users(
        self,
        tenant_id: str,
        limit: int = 20,
        keyword: Optional[str] = None,
        status: Optional[str] = None,
        role_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询用户列表

        支持分页、搜索和筛选的用户列表查询
        """
        try:
            # 构建查询条件
            conditions = [
                self.user_model.tenant_id == tenant_id,
                self.user_model.status != "deleted",
            ]

            # 添加状态筛选
            if status:
                conditions.append(self.user_model.status == status)

            # 添加关键词搜索
            if keyword:
                keyword_condition = or_(
                    self.user_model.username.ilike(f"%{keyword}%"),
                    self.user_model.email.ilike(f"%{keyword}%"),
                    self.user_model.nickname.ilike(f"%{keyword}%"),
                )
                conditions.append(keyword_condition)

            # 添加角色筛选
            if role_id:
                # 子查询获取拥有指定角色的用户ID
                role_subquery = select(self.user_role_model.user_id).where(
                    and_(
                        self.user_role_model.role_id == role_id,
                        self.user_role_model.tenant_id == tenant_id,
                        self.user_role_model.status == CommonStatus.ACTIVE,
                    )
                )
                conditions.append(self.user_model.user_id.in_(role_subquery))

            # 构建基础查询
            base_query = select(self.user_model).where(and_(*conditions))
            # 执行分页查询
            query = base_query.order_by(self.user_model.user_id).limit(limit + 1)
            result = await self.session.execute(query)
            users = result.scalars().all()

            # 获取用户角色信息
            user_ids = [user.user_id for user in users]
            user_roles_map = await self._get_users_roles_batch(user_ids)

            # 格式化返回数据
            items = []
            for user in users:
                user_roles = user_roles_map.get(user.user_id, [])

                items.append(
                    {
                        "user_id": user.user_id,
                        "username": user.username,
                        "email": user.email,
                        "phone": user.phone,
                        "nickname": user.nickname,
                        "status": user.status,
                        "created_at": user.created_at.isoformat(),
                        "updated_at": (user.updated_at.isoformat() if user.updated_at else None),
                        "last_login": getattr(user, "last_login_at", None),
                        "login_count": getattr(user, "login_count", 0),
                        "roles": user_roles,
                        "profile": user.profile or {},
                    }
                )

            # 获取总数（仅在第一页时计算）
            count_query = select(func.count()).select_from(
                select(self.user_model.user_id).where(and_(*conditions)).subquery()
            )
            count_result = await self.session.execute(count_query)
            total = count_result.scalar() or 0

            return {"items": items, "total": total, "limit": limit}

        except Exception as e:
            raise BusinessError(f"查询用户列表失败: {str(e)}")

    async def get_user_detail(
        self,
        tenant_id: str,
        user_id: str,
        include_permissions: bool = False,
        include_sessions: bool = False,
    ) -> Dict[str, Any]:
        """
        获取用户详情

        根据用户ID获取完整的用户详细信息
        """
        try:
            # 验证用户存在性和租户权限
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 查询用户角色列表
            user_roles = await self._get_user_all_roles(user_id)

            # 获取用户权限列表（如果需要）
            permissions = []
            if include_permissions:
                permissions_result = await self.get_user_permissions(
                    user_id=user_id, tenant_id=tenant_id, include_inherited=True
                )
                permissions = permissions_result.get("permissions", [])

            # 获取用户会话信息（如果需要）
            active_sessions_count = 0
            if include_sessions:
                sessions_result = await self.get_user_sessions(
                    user_id=user_id, tenant_id=tenant_id, status="active"
                )
                active_sessions_count = sessions_result.get("total_sessions", 0)

            # 获取安全相关信息
            security_info = await self._get_user_security_info(user_id)

            return {
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "nickname": user.nickname,
                "status": user.status,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "last_login": getattr(user, "last_login_at", None),
                "login_count": getattr(user, "login_count", 0),
                "failed_login_attempts": getattr(user, "failed_login_attempts", 0),
                "locked_until": getattr(user, "locked_until", None),
                "password_changed_at": security_info.get("password_changed_at"),
                "roles": user_roles,
                "profile": user.profile or {},
                "permissions": permissions,
                "security_settings": {
                    "mfa_enabled": security_info.get("mfa_enabled", False),
                    "password_expires_at": security_info.get("password_expires_at"),
                    "last_password_change": security_info.get("password_changed_at"),
                    "last_login": getattr(user, "last_login_at", None),
                    "active_sessions": active_sessions_count,
                },
            }

        except Exception as e:
            raise BusinessError(f"获取用户详情失败: {str(e)}")

    async def get_user_profile(self, tenant_id: str, user_id: str) -> Dict[str, Any]:
        """
        获取用户个人资料

        获取用户的个人资料和偏好设置
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 获取用户偏好设置
            preferences = await self._get_user_preferences(user_id)

            # 获取安全信息
            security_info = await self._get_user_security_info(user_id)

            return {
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "nickname": user.nickname,
                "profile": user.profile or {},
                "preferences": preferences,
                "security_info": {
                    "mfa_enabled": security_info.get("mfa_enabled", False),
                    "password_expires_at": security_info.get("password_expires_at"),
                    "last_password_change": security_info.get("password_changed_at"),
                    "last_login": getattr(user, "last_login_at", None),
                    "active_sessions": security_info.get("active_sessions", 0),
                },
            }

        except Exception as e:
            raise BusinessError(f"获取用户个人资料失败: {str(e)}")

    async def update_user(
        self,
        tenant_id: str,
        user_id: str,
        nickname: Optional[str] = None,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        status: Optional[str] = None,
        profile: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        更新用户信息

        支持部分字段更新的用户信息修改
        """
        try:
            # 验证用户存在性和修改权限
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 记录原始值用于审计
            original_values = {
                "email": user.email,
                "phone": user.phone,
                "nickname": user.nickname,
                "status": user.status,
                "profile": user.profile,
            }

            # 检查新邮箱唯一性（如果要更新邮箱）
            if email and email != user.email:
                await self._check_email_uniqueness(tenant_id, email, user_id)
                user.email = email

            # 检查新手机号唯一性（如果要更新手机号）
            if phone and phone != user.phone:
                await self._check_phone_uniqueness(tenant_id, phone, user_id)
                user.phone = phone

            # 更新昵称
            if nickname is not None:
                user.nickname = nickname

            # 验证状态变更的合法性并更新
            if status and status != user.status:
                await self._validate_status_transition(user.status, status)
                user.status = status

            # 更新个人资料
            if profile is not None:
                # 合并现有资料和新资料
                current_profile = user.profile or {}
                current_profile.update(profile)
                user.profile = current_profile

            # 更新时间戳
            user.updated_at = datetime.now()

            await self.session.commit()

            # 清理相关缓存
            await self._clear_user_cache(user_id)

            # 获取更新后的用户角色
            user_roles = await self._get_user_all_roles(user_id)

            # 记录修改审计日志
            changes = {}
            for key, original_value in original_values.items():
                new_value = getattr(user, key)
                if original_value != new_value:
                    changes[key] = {"old": original_value, "new": new_value}

            if changes:
                await self._create_audit_log(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    action="UPDATE_USER",
                    resource_type="USER",
                    resource_id=user_id,
                    details={"changes": changes},
                )

            return {
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "nickname": user.nickname,
                "status": user.status,
                "created_at": user.created_at.isoformat(),
                "updated_at": user.updated_at.isoformat() if user.updated_at else "",
                "last_login": getattr(user, "last_login_at", None),
                "login_count": getattr(user, "login_count", 0),
                "roles": user_roles,
                "profile": user.profile or {},
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新用户信息失败: {str(e)}")

    async def update_user_preferences(
        self, tenant_id: str, user_id: str, preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新用户偏好设置

        更新用户的个人偏好和界面设置
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 获取当前偏好设置
            current_preferences = await self._get_user_preferences(user_id)

            # 合并新的偏好设置
            updated_preferences = current_preferences.copy()
            updated_preferences.update(preferences)

            # 验证偏好设置的有效性
            await self._validate_preferences(updated_preferences)

            # 保存到Redis
            await self.redis_cache_service.set(
                f"user_preferences:{user_id}",
                updated_preferences,
                ttl=86400 * 30,  # 30天过期
            )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="UPDATE_USER_PREFERENCES",
                resource_type="USER",
                resource_id=user_id,
                details={"updated_preferences": preferences},
            )

            # 获取安全信息
            security_info = await self._get_user_security_info(user_id)

            return {
                "user_id": user.user_id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "nickname": user.nickname,
                "profile": user.profile or {},
                "preferences": updated_preferences,
                "security_info": {
                    "mfa_enabled": security_info.get("mfa_enabled", False),
                    "password_expires_at": security_info.get("password_expires_at"),
                    "last_password_change": security_info.get("password_changed_at"),
                    "last_login": getattr(user, "last_login_at", None),
                    "active_sessions": security_info.get("active_sessions", 0),
                },
            }

        except Exception as e:
            raise BusinessError(f"更新用户偏好设置失败: {str(e)}")

    async def update_user_status(
        self,
        tenant_id: str,
        user_id: str,
        status: str,
        reason: Optional[str] = None,
        locked_until: Optional[str] = None,
        send_notification: bool = True,
    ) -> Dict[str, Any]:
        """
        用户状态管理

        更新用户的状态，支持锁定、解锁、暂停等操作
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            old_status = user.status

            # 验证状态转换规则
            await self._validate_status_transition(old_status, status)

            # 更新用户状态
            user.status = status
            user.updated_at = datetime.now()

            # 处理锁定时间
            locked_until_dt = None
            if status == "locked" and locked_until:
                locked_until_dt = datetime.fromisoformat(locked_until)
                if locked_until_dt <= datetime.now():
                    raise ValidationError("锁定时间不能早于当前时间")
                # TODO: 将锁定时间存储到用户表的locked_until字段
                # user.locked_until = locked_until_dt

            await self.session.commit()

            # 清理用户缓存
            await self._clear_user_cache(user_id)

            # 处理状态变更的副作用
            if status == "locked":
                # 终止所有活跃会话
                await self.terminate_all_user_sessions(
                    user_id=user_id, tenant_id=tenant_id, reason=f"用户被锁定: {reason}"
                )
            elif status == "deleted":
                # 软删除时清理会话和令牌
                await self.terminate_all_user_sessions(
                    user_id=user_id, tenant_id=tenant_id, reason=f"用户被删除: {reason}"
                )

            # 发送通知
            if send_notification:
                await self._send_status_change_notification(
                    user_id=user_id,
                    old_status=old_status,
                    new_status=status,
                    reason=reason,
                )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="UPDATE_USER_STATUS",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "old_status": old_status,
                    "new_status": status,
                    "reason": reason,
                    "locked_until": locked_until,
                },
            )

            return {
                "user_id": user_id,
                "old_status": old_status,
                "new_status": status,
                "locked_until": locked_until,
                "updated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更新用户状态失败: {str(e)}")

    async def delete_user(
        self,
        tenant_id: str,
        user_id: str,
        delete_type: str = "soft",
        reason: Optional[str] = None,
        transfer_data_to: Optional[str] = None,
        confirmation_code: str = None,
    ) -> Dict[str, Any]:
        """
        删除用户

        支持软删除和硬删除的用户删除操作
        """
        try:
            # 验证删除类型
            if delete_type not in ["soft", "hard"]:
                raise ValidationError("删除类型必须是 'soft' 或 'hard'")

            # 验证用户存在性和删除权限
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 检查用户依赖关系（创建的内容等）
            dependencies = await self._check_user_dependencies(user_id)
            if dependencies["has_dependencies"] and delete_type == "hard":
                raise ValidationError(
                    f"用户存在依赖关系，无法硬删除: {dependencies['dependencies']}"
                )

            # 终止所有用户会话
            sessions_result = await self.terminate_all_user_sessions(
                user_id=user_id, tenant_id=tenant_id, reason=f"用户删除: {reason}"
            )
            terminated_sessions = sessions_result.get("terminated_count", 0)

            # 移除角色和权限关联
            await self._remove_all_user_roles(user_id, tenant_id)

            # 处理用户数据转移（如果指定）
            data_transferred = False
            if transfer_data_to:
                await self._transfer_user_data(user_id, transfer_data_to)
                data_transferred = True

            if delete_type == "soft":
                # 软删除：更新状态为deleted
                user.status = "deleted"
                user.updated_at = datetime.now()
                # TODO: 设置deleted_at字段
                # user.deleted_at = datetime.now()
            else:
                # 硬删除：从数据库中删除记录
                await self.session.delete(user)

            await self.session.commit()

            # 清理所有相关缓存
            await self._clear_user_cache(user_id)

            # 记录删除审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="DELETE_USER",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "delete_type": delete_type,
                    "reason": reason,
                    "data_transferred": data_transferred,
                    "transfer_target": transfer_data_to,
                    "sessions_terminated": terminated_sessions,
                },
            )

            return {
                "user_id": user_id,
                "status": "deleted",
                "delete_type": delete_type,
                "deleted_at": datetime.now().isoformat(),
                "data_transferred": data_transferred,
                "sessions_terminated": terminated_sessions,
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"删除用户失败: {str(e)}")

    async def change_phone(
        self,
        tenant_id: str,
        user_id: str,
        old_phone: str,
        new_phone: str,
        sms_code: str,
        code_id: str,
    ) -> Dict[str, Any]:
        """
        更换手机号

        需要短信验证码验证的手机号更换
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证旧手机号是否匹配
            if user.phone != old_phone:
                raise ValidationError("当前手机号不匹配")

            # 验证短信验证码
            await self._verify_code(code_id, sms_code, "change_phone")

            # 检查新手机号唯一性
            await self._check_phone_uniqueness(tenant_id, new_phone, user_id)

            # 更新用户手机号
            user.phone = new_phone
            user.updated_at = datetime.now()
            await self.session.commit()

            # 清理用户缓存
            await self._clear_user_cache(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="CHANGE_PHONE",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "old_phone": f"{old_phone[:3]}****{old_phone[-4:]}",
                    "new_phone": f"{new_phone[:3]}****{new_phone[-4:]}",
                },
            )

            return {
                "user_id": user_id,
                "old_value": f"{old_phone[:3]}****{old_phone[-4:]}",
                "new_value": f"{new_phone[:3]}****{new_phone[-4:]}",
                "changed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更换手机号失败: {str(e)}")

    async def change_email(
        self,
        tenant_id: str,
        user_id: str,
        old_email: str,
        new_email: str,
        email_code: str,
        code_id: str,
    ) -> Dict[str, Any]:
        """
        更换邮箱

        需要邮箱验证码验证的邮箱更换
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证旧邮箱是否匹配
            if user.email != old_email:
                raise ValidationError("当前邮箱不匹配")

            # 验证邮箱验证码
            await self._verify_code(code_id, email_code, "change_email")

            # 检查新邮箱唯一性
            await self._check_email_uniqueness(tenant_id, new_email, user_id)

            # 更新用户邮箱
            user.email = new_email
            user.updated_at = datetime.now()
            await self.session.commit()

            # 清理用户缓存
            await self._clear_user_cache(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="CHANGE_EMAIL",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "old_email": f"{old_email.split('@')[0][:2]}****@{old_email.split('@')[1]}",
                    "new_email": f"{new_email.split('@')[0][:2]}****@{new_email.split('@')[1]}",
                },
            )

            return {
                "user_id": user_id,
                "old_value": f"{old_email.split('@')[0][:2]}****@{old_email.split('@')[1]}",
                "new_value": f"{new_email.split('@')[0][:2]}****@{new_email.split('@')[1]}",
                "changed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"更换邮箱失败: {str(e)}")

    async def send_sms_code(
        self, tenant_id: str, phone: str, scene: str, user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送短信验证码

        支持多种场景的短信验证码发送
        """
        try:
            # 验证发送频率限制
            await self._check_send_frequency_limit(phone, "sms", scene)

            # 生成验证码
            verification_code = self._generate_verification_code()
            code_id = f"sms_code_{uuid.uuid4()}"

            # 存储验证码到Redis
            code_data = {
                "code": verification_code,
                "phone": phone,
                "scene": scene,
                "user_id": user_id,
                "created_at": datetime.now().isoformat(),
                "attempts": 0,
            }
            await self.redis_cache_service.set(
                f"verification_code:{code_id}", code_data, ttl=300  # 5分钟过期
            )

            # 发送短信
            await self._send_sms(phone, verification_code, scene)

            # 记录发送频率
            await self._record_send_frequency(phone, "sms", scene)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="SEND_SMS_CODE",
                resource_type="VERIFICATION",
                resource_id=code_id,
                details={"phone": phone, "scene": scene},
            )

            return {
                "target": f"{phone[:3]}****{phone[-4:]}",
                "code_id": code_id,
                "expire_seconds": 300,
                "sent_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"发送短信验证码失败: {str(e)}")

    async def send_email_code(
        self, tenant_id: str, email: str, scene: str, user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送邮箱验证码

        支持多种场景的邮箱验证码发送
        """
        try:
            # 验证发送频率限制
            await self._check_send_frequency_limit(email, "email", scene)

            # 生成验证码
            verification_code = self._generate_verification_code()
            code_id = f"email_code_{uuid.uuid4()}"

            # 存储验证码到Redis
            code_data = {
                "code": verification_code,
                "email": email,
                "scene": scene,
                "user_id": user_id,
                "created_at": datetime.now().isoformat(),
                "attempts": 0,
            }
            await self.redis_cache_service.set(
                f"verification_code:{code_id}",
                json.dumps(code_data, ensure_ascii=False),
                ttl=300,  # 5分钟过期
            )

            # 发送邮件
            await self._send_email(email, verification_code, scene)

            # 记录发送频率
            await self._record_send_frequency(email, "email", scene)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="SEND_EMAIL_CODE",
                resource_type="VERIFICATION",
                resource_id=code_id,
                details={"email": email, "scene": scene},
            )

            return {
                "target": f"{email.split('@')[0][:2]}****@{email.split('@')[1]}",
                "code_id": code_id,
                "expire_seconds": 300,
                "sent_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"发送邮箱验证码失败: {str(e)}")

    async def assign_roles(
        self,
        user_id: str,
        tenant_id: str,
        role_ids: List[str],
        assignment_type: str = "permanent",
        expires_at: Optional[str] = None,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        为用户分配角色

        支持永久分配和临时分配
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证角色存在性和有效性
            await self._validate_roles_exist(tenant_id, role_ids)

            # 检查用户是否已经拥有这些角色
            existing_roles = await self._get_user_existing_roles(user_id, role_ids)
            new_roles = [role_id for role_id in role_ids if role_id not in existing_roles]

            if not new_roles:
                return {
                    "user_id": user_id,
                    "assigned_roles": [],
                    "failed_roles": [
                        {"role_id": role_id, "reason": "角色已分配"} for role_id in role_ids
                    ],
                    "assigned_at": datetime.now().isoformat(),
                }

            # 解析过期时间
            expires_dt = None
            if assignment_type == "temporary" and expires_at:
                expires_dt = datetime.fromisoformat(expires_at)
                if expires_dt <= datetime.now():
                    raise ValidationError("过期时间不能早于当前时间")

            # 分配角色
            assigned_roles = []
            failed_roles = []

            for role_id in new_roles:
                try:
                    # 创建用户角色关联
                    user_role = self.user_role_model(
                        user_id=user_id,
                        role_id=role_id,
                        tenant_id=tenant_id,
                        assignment_type=assignment_type,
                        assigned_at=datetime.now(),
                        expires_at=expires_dt,
                        assigned_by=None,  # TODO: 从上下文获取操作者ID
                        reason=reason,
                    )
                    self.session.add(user_role)

                    # 获取角色信息
                    role_info = await self._get_role_info(role_id)
                    assigned_roles.append(
                        {
                            "role_id": role_id,
                            "role_name": role_info.get("name", ""),
                            "role_code": role_info.get("code", ""),
                            "assigned_at": datetime.now().isoformat(),
                            "assignment_type": assignment_type,
                            "expires_at": (expires_dt.isoformat() if expires_dt else None),
                        }
                    )

                except Exception as e:
                    failed_roles.append({"role_id": role_id, "reason": f"分配失败: {str(e)}"})

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="ASSIGN_USER_ROLES",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "role_ids": new_roles,
                    "assignment_type": assignment_type,
                    "expires_at": expires_at,
                    "reason": reason,
                },
            )

            await self.session.commit()

            # 清除用户权限缓存
            await self._clear_user_permissions_cache(user_id)

            return {
                "user_id": user_id,
                "assigned_roles": assigned_roles,
                "failed_roles": failed_roles,
                "assigned_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"分配角色失败: {str(e)}")

    async def remove_roles(
        self,
        user_id: str,
        tenant_id: str,
        role_ids: List[str],
        reason: Optional[str] = None,
        force_remove: bool = False,
    ) -> Dict[str, Any]:
        """
        移除用户角色

        支持依赖检查和强制移除
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 检查用户当前拥有的角色
            user_roles = await self._get_user_existing_roles(user_id, role_ids)
            roles_to_remove = [role_id for role_id in role_ids if role_id in user_roles]

            if not roles_to_remove:
                return {
                    "user_id": user_id,
                    "removed_roles": [],
                    "failed_roles": [
                        {"role_id": role_id, "reason": "用户未拥有此角色"} for role_id in role_ids
                    ],
                    "remaining_roles": await self._get_user_all_roles(user_id),
                    "removed_at": datetime.now().isoformat(),
                }

            # 检查角色依赖关系（除非强制移除）
            if not force_remove:
                dependency_check = await self._check_role_dependencies(user_id, roles_to_remove)
                if dependency_check["has_dependencies"]:
                    raise ValidationError(
                        f"存在角色依赖关系，无法移除: {dependency_check['dependencies']}"
                    )

            # 移除角色
            removed_roles = []
            failed_roles = []

            for role_id in roles_to_remove:
                try:
                    # 删除用户角色关联
                    stmt = select(self.user_role_model).where(
                        and_(
                            self.user_role_model.user_id == user_id,
                            self.user_role_model.role_id == role_id,
                            self.user_role_model.tenant_id == tenant_id,
                        )
                    )
                    result = await self.session.execute(stmt)
                    user_role = result.scalar_one_or_none()

                    if user_role:
                        await self.session.delete(user_role)
                        removed_roles.append(role_id)
                    else:
                        failed_roles.append({"role_id": role_id, "reason": "角色关联不存在"})

                except Exception as e:
                    failed_roles.append({"role_id": role_id, "reason": f"移除失败: {str(e)}"})

            # 获取移除后剩余的角色
            remaining_roles = await self._get_user_all_roles(user_id)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="REMOVE_USER_ROLES",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "role_ids": removed_roles,
                    "reason": reason,
                    "force_remove": force_remove,
                },
            )

            await self.session.commit()

            # 清除用户权限缓存
            await self._clear_user_permissions_cache(user_id)

            return {
                "user_id": user_id,
                "removed_roles": removed_roles,
                "failed_roles": failed_roles,
                "remaining_roles": remaining_roles,
                "removed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"移除角色失败: {str(e)}")

    async def get_user_permissions(
        self,
        user_id: str,
        tenant_id: str,
        resource_type: Optional[str] = None,
        include_inherited: bool = True,
    ) -> Dict[str, Any]:
        """
        查询用户有效权限

        支持按资源类型筛选和权限继承
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 从缓存获取权限信息
            cache_key = f"user_permissions:{user_id}:{resource_type or 'all'}"
            cached_permissions = await self.redis_cache_service.get(cache_key)

            if cached_permissions:
                return cached_permissions

            # 查询用户直接权限和角色权限
            permissions = await self._get_user_effective_permissions(
                user_id, tenant_id, resource_type, include_inherited
            )

            # 缓存权限信息
            await self.redis_cache_service.set(cache_key, permissions, ttl=1800)  # 30分钟过期

            return permissions

        except Exception as e:
            raise BusinessError(f"查询用户权限失败: {str(e)}")

    async def get_user_permission_sources(
        self, user_id: str, tenant_id: str, permission_code: str
    ) -> Dict[str, Any]:
        """
        查询用户权限来源

        分析权限的具体来源（直接分配、角色继承等）
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            sources = await self._analyze_permission_sources(user_id, tenant_id, permission_code)

            return {
                "user_id": user_id,
                "permission_code": permission_code,
                "has_permission": len(sources) > 0,
                "sources": sources,
                "analyzed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"查询权限来源失败: {str(e)}")

    async def get_user_role_history(
        self,
        user_id: str,
        tenant_id: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 50,
    ) -> Dict[str, Any]:
        """
        查询用户角色历史

        包括角色分配和移除的历史记录
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 解析时间范围
            start_dt = datetime.fromisoformat(start_date) if start_date else None
            end_dt = datetime.fromisoformat(end_date) if end_date else None

            # 查询角色历史
            history = await self._get_user_role_history(user_id, start_dt, end_dt, limit)

            return {
                "user_id": user_id,
                "total_records": len(history),
                "history": history,
                "queried_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"查询角色历史失败: {str(e)}")

    async def get_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        status: Optional[str] = None,
        limit: int = 20,
    ) -> Dict[str, Any]:
        """
        查询用户活跃会话

        获取用户当前的活跃会话信息
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 从Redis查询会话信息
            sessions = await self._get_user_active_sessions(user_id, status, limit)

            return {
                "user_id": user_id,
                "total_sessions": len(sessions),
                "sessions": sessions,
                "queried_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"查询用户会话失败: {str(e)}")

    async def terminate_user_session(
        self,
        user_id: str,
        tenant_id: str,
        session_id: str,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        强制下线指定会话

        终止用户的特定会话
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 验证会话存在性
            session_info = await self._get_session_info(session_id)
            if not session_info or session_info.get("user_id") != user_id:
                raise NotFoundError("会话不存在或不属于该用户")

            # 终止会话
            await self._terminate_session(session_id, reason)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="TERMINATE_USER_SESSION",
                resource_type="SESSION",
                resource_id=session_id,
                details={"target_user_id": user_id, "reason": reason},
            )

            return {
                "user_id": user_id,
                "session_id": session_id,
                "status": "terminated",
                "terminated_at": datetime.now().isoformat(),
                "reason": reason,
            }

        except Exception as e:
            raise BusinessError(f"终止会话失败: {str(e)}")

    async def terminate_all_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        exclude_current: bool = True,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        强制下线所有会话

        终止用户的所有活跃会话
        """
        try:
            # 验证用户存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            # 获取所有活跃会话
            sessions = await self._get_user_active_sessions(user_id, "active")

            # TODO: 如果exclude_current为True，需要排除当前会话
            # current_session_id = self._get_current_session_id()

            terminated_count = 0
            failed_sessions = []

            for session in sessions:
                try:
                    await self._terminate_session(session["session_id"], reason)
                    terminated_count += 1
                except Exception as e:
                    failed_sessions.append({"session_id": session["session_id"], "error": str(e)})

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,  # TODO: 从上下文获取操作者ID
                action="TERMINATE_ALL_USER_SESSIONS",
                resource_type="USER",
                resource_id=user_id,
                details={
                    "terminated_count": terminated_count,
                    "failed_count": len(failed_sessions),
                    "reason": reason,
                },
            )

            return {
                "user_id": user_id,
                "total_sessions": len(sessions),
                "terminated_count": terminated_count,
                "failed_sessions": failed_sessions,
                "terminated_at": datetime.now().isoformat(),
                "reason": reason,
            }

        except Exception as e:
            raise BusinessError(f"终止所有会话失败: {str(e)}")

    async def batch_assign_roles(
        self,
        tenant_id: str,
        user_ids: List[str],
        role_ids: List[str],
        assignment_type: str = "permanent",
        expires_at: Optional[str] = None,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        批量角色分配

        为多个用户批量分配角色
        """
        try:
            # 验证角色存在性
            await self._validate_roles_exist(tenant_id, role_ids)

            # 批量处理结果
            successful_assignments = []
            failed_assignments = []

            for user_id in user_ids:
                try:
                    result = await self.assign_roles(
                        user_id=user_id,
                        tenant_id=tenant_id,
                        role_ids=role_ids,
                        assignment_type=assignment_type,
                        expires_at=expires_at,
                        reason=reason,
                    )
                    successful_assignments.append(
                        {"user_id": user_id, "assigned_roles": result["assigned_roles"]}
                    )
                except Exception as e:
                    failed_assignments.append({"user_id": user_id, "error": str(e)})

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_ASSIGN_ROLES",
                resource_type="USER",
                resource_id="batch",
                details={
                    "user_ids": user_ids,
                    "role_ids": role_ids,
                    "successful_count": len(successful_assignments),
                    "failed_count": len(failed_assignments),
                },
            )

            return {
                "total_users": len(user_ids),
                "successful_count": len(successful_assignments),
                "failed_count": len(failed_assignments),
                "successful_assignments": successful_assignments,
                "failed_assignments": failed_assignments,
                "processed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"批量分配角色失败: {str(e)}")

    async def batch_update_status(
        self,
        tenant_id: str,
        user_ids: List[str],
        status: str,
        reason: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        批量状态更新

        批量更新用户状态
        """
        try:
            # 验证状态有效性
            if status not in self.USER_STATUS.values():
                raise ValidationError(f"无效的用户状态: {status}")

            successful_updates = []
            failed_updates = []

            for user_id in user_ids:
                try:
                    result = await self.update_user_status(
                        tenant_id=tenant_id,
                        user_id=user_id,
                        status=status,
                        reason=reason,
                    )
                    successful_updates.append(
                        {
                            "user_id": user_id,
                            "old_status": result.get("old_status"),
                            "new_status": status,
                        }
                    )
                except Exception as e:
                    failed_updates.append({"user_id": user_id, "error": str(e)})

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_UPDATE_STATUS",
                resource_type="USER",
                resource_id="batch",
                details={
                    "user_ids": user_ids,
                    "status": status,
                    "successful_count": len(successful_updates),
                    "failed_count": len(failed_updates),
                    "reason": reason,
                },
            )

            return {
                "total_users": len(user_ids),
                "successful_count": len(successful_updates),
                "failed_count": len(failed_updates),
                "successful_updates": successful_updates,
                "failed_updates": failed_updates,
                "processed_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"批量更新状态失败: {str(e)}")

    async def get_user_activity_statistics(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        用户活跃度统计

        统计用户的活跃度数据
        """
        try:
            # 解析时间范围
            start_dt = (
                datetime.fromisoformat(start_date)
                if start_date
                else datetime.now() - timedelta(days=30)
            )
            end_dt = datetime.fromisoformat(end_date) if end_date else datetime.now()

            # 查询活跃度数据
            activity_data = await self._calculate_user_activity(
                tenant_id, user_id, start_dt, end_dt
            )

            return {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "period": {
                    "start_date": start_dt.isoformat(),
                    "end_date": end_dt.isoformat(),
                },
                "statistics": activity_data,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"获取活跃度统计失败: {str(e)}")

    async def get_user_login_statistics(
        self, tenant_id: str, user_id: Optional[str] = None, period: str = "month"
    ) -> Dict[str, Any]:
        """
        用户登录统计

        统计用户的登录数据
        """
        try:
            # 计算时间范围
            if period == "day":
                start_dt = datetime.now() - timedelta(days=1)
            elif period == "week":
                start_dt = datetime.now() - timedelta(weeks=1)
            elif period == "month":
                start_dt = datetime.now() - timedelta(days=30)
            else:
                start_dt = datetime.now() - timedelta(days=30)

            # 查询登录统计数据
            login_data = await self._calculate_login_statistics(tenant_id, user_id, start_dt)

            return {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "period": period,
                "statistics": login_data,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"获取登录统计失败: {str(e)}")

    async def export_user_data(
        self,
        tenant_id: str,
        export_type: str = "basic",
        user_ids: Optional[List[str]] = None,
        format: str = "json",
    ) -> Dict[str, Any]:
        """
        导出用户数据

        支持多种格式的用户数据导出
        """
        try:
            # 查询用户数据
            if export_type == "basic":
                data = await self._export_basic_user_data(tenant_id, user_ids)
            elif export_type == "detailed":
                data = await self._export_detailed_user_data(tenant_id, user_ids)
            elif export_type == "audit_logs":
                data = await self._export_user_audit_logs(tenant_id, user_ids)
            else:
                raise ValidationError(f"不支持的导出类型: {export_type}")

            # 生成导出文件
            export_id = f"export_{uuid.uuid4()}"
            file_info = await self._generate_export_file(export_id, data, format)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="EXPORT_USER_DATA",
                resource_type="USER",
                resource_id="batch",
                details={
                    "export_type": export_type,
                    "format": format,
                    "user_count": len(user_ids) if user_ids else "all",
                    "export_id": export_id,
                },
            )

            return {
                "export_id": export_id,
                "export_type": export_type,
                "format": format,
                "file_info": file_info,
                "record_count": len(data),
                "exported_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"导出用户数据失败: {str(e)}")

    async def batch_import_users(
        self,
        tenant_id: str,
        import_data: List[Dict[str, Any]],
        import_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        批量导入用户

        支持从文件或数据批量创建用户
        """
        try:
            import_options = import_options or {}

            # 导入配置
            skip_duplicates = import_options.get("skip_duplicates", True)
            send_welcome_email = import_options.get("send_welcome_email", False)
            default_password = import_options.get("default_password", "TempPass123!")
            default_status = import_options.get("default_status", "pending")

            # 批量处理结果
            successful_imports = []
            failed_imports = []
            duplicate_users = []

            # 验证租户存在性
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant or tenant.status != CommonStatus.ACTIVE:
                raise NotFoundError("租户不存在或未激活")

            for index, user_data in enumerate(import_data):
                try:
                    # 验证必需字段
                    if not user_data.get("username") or not user_data.get("email"):
                        failed_imports.append(
                            {
                                "index": index,
                                "data": user_data,
                                "error": "缺少必需字段：username 或 email",
                            }
                        )
                        continue

                    # 检查用户是否已存在
                    existing_user = await self._check_user_exists(
                        tenant_id,
                        user_data.get("username"),
                        user_data.get("email"),
                        user_data.get("phone"),
                    )

                    if existing_user:
                        if skip_duplicates:
                            duplicate_users.append(
                                {
                                    "index": index,
                                    "username": user_data.get("username"),
                                    "email": user_data.get("email"),
                                    "existing_user_id": existing_user.user_id,
                                }
                            )
                            continue
                        else:
                            failed_imports.append(
                                {
                                    "index": index,
                                    "data": user_data,
                                    "error": "用户已存在",
                                }
                            )
                            continue

                    # 创建用户
                    result = await self.create_user(
                        tenant_id=tenant_id,
                        username=user_data.get("username"),
                        email=user_data.get("email"),
                        phone=user_data.get("phone"),
                        nickname=user_data.get("nickname"),
                        password=user_data.get("password", default_password),
                        status=user_data.get("status", default_status),
                        profile=user_data.get("profile", {}),
                        send_welcome_email=send_welcome_email,
                    )

                    successful_imports.append(
                        {
                            "index": index,
                            "user_id": result["user_id"],
                            "username": result["username"],
                            "email": result["email"],
                        }
                    )

                except Exception as e:
                    failed_imports.append({"index": index, "data": user_data, "error": str(e)})

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_IMPORT_USERS",
                resource_type="USER",
                resource_id="batch",
                details={
                    "total_records": len(import_data),
                    "successful_count": len(successful_imports),
                    "failed_count": len(failed_imports),
                    "duplicate_count": len(duplicate_users),
                },
            )

            return {
                "total_records": len(import_data),
                "successful_count": len(successful_imports),
                "failed_count": len(failed_imports),
                "duplicate_count": len(duplicate_users),
                "successful_imports": successful_imports,
                "failed_imports": failed_imports,
                "duplicate_users": duplicate_users,
                "imported_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"批量导入用户失败: {str(e)}")

    async def batch_export_users(
        self, tenant_id: str, export_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        批量导出用户

        导出租户下的所有用户数据
        """
        try:
            export_options = export_options or {}

            # 导出配置
            include_roles = export_options.get("include_roles", True)
            include_permissions = export_options.get("include_permissions", False)
            include_profile = export_options.get("include_profile", True)
            export_format = export_options.get("format", "json")
            status_filter = export_options.get("status_filter")

            # 查询用户数据
            stmt = select(self.user_model).where(
                and_(
                    self.user_model.tenant_id == tenant_id,
                    self.user_model.status != "deleted",
                )
            )

            if status_filter:
                stmt = stmt.where(self.user_model.status == status_filter)

            result = await self.session.execute(stmt)
            users = result.scalars().all()

            # 构建导出数据
            export_data = []
            for user in users:
                user_data = {
                    "user_id": user.user_id,
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone,
                    "nickname": user.nickname,
                    "status": user.status,
                    "created_at": (user.created_at.isoformat() if user.created_at else None),
                    "updated_at": (user.updated_at.isoformat() if user.updated_at else None),
                    "last_login": (user.last_login.isoformat() if user.last_login else None),
                    "roles": [],
                }

                # 包含用户资料
                if include_profile and hasattr(user, "profile"):
                    user_data["profile"] = user.profile

                # 包含用户角色
                if include_roles:
                    user_data["roles"] = await self._get_user_all_roles(user.user_id)

                # 包含用户权限
                if include_permissions:
                    permissions = await self._get_user_effective_permissions(
                        user.user_id, tenant_id
                    )
                    user_data["permissions"] = permissions["permissions"]

                export_data.append(user_data)

            # 生成导出文件
            export_id = f"export_{uuid.uuid4()}"
            file_info = await self._generate_export_file(export_id, export_data, export_format)

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="BATCH_EXPORT_USERS",
                resource_type="USER",
                resource_id="batch",
                details={
                    "export_id": export_id,
                    "user_count": len(export_data),
                    "format": export_format,
                    "include_roles": include_roles,
                    "include_permissions": include_permissions,
                },
            )

            return {
                "export_id": export_id,
                "user_count": len(export_data),
                "file_info": file_info,
                "export_options": export_options,
                "exported_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"批量导出用户失败: {str(e)}")

    async def send_user_notification(
        self,
        tenant_id: str,
        user_ids: List[str],
        notification_type: str,
        title: str,
        content: str,
        channels: List[str],
        priority: str = "normal",
        scheduled_at: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        发送用户通知

        支持多种通知渠道和批量发送
        """
        try:
            # 验证通知类型和渠道
            valid_types = [
                "system",
                "security",
                "marketing",
                "reminder",
                "announcement",
            ]
            valid_channels = ["email", "sms", "push", "in_app"]
            valid_priorities = ["low", "normal", "high", "urgent"]

            if notification_type not in valid_types:
                raise ValidationError(f"无效的通知类型: {notification_type}")

            if not all(channel in valid_channels for channel in channels):
                raise ValidationError(f"无效的通知渠道，支持的渠道: {valid_channels}")

            if priority not in valid_priorities:
                raise ValidationError(f"无效的优先级，支持的优先级: {valid_priorities}")

            # 验证用户存在性
            valid_users = []
            invalid_users = []

            for user_id in user_ids:
                user = await self._get_user_by_id(user_id, tenant_id)
                if user and user.status in ["active", "inactive"]:
                    valid_users.append(user)
                else:
                    invalid_users.append(user_id)

            if not valid_users:
                raise ValidationError("没有有效的用户可以发送通知")

            # 生成通知ID
            notification_id = f"notification_{uuid.uuid4()}"

            # 解析计划发送时间
            scheduled_dt = None
            if scheduled_at:
                scheduled_dt = datetime.fromisoformat(scheduled_at)
                if scheduled_dt <= datetime.now():
                    raise ValidationError("计划发送时间不能早于当前时间")

            # 发送通知
            sent_notifications = []
            failed_notifications = []

            for user in valid_users:
                for channel in channels:
                    try:
                        # 检查用户是否启用了该通知渠道
                        if not await self._is_notification_channel_enabled(user.user_id, channel):
                            continue

                        # 发送通知
                        send_result = await self._send_notification(
                            user=user,
                            channel=channel,
                            notification_type=notification_type,
                            title=title,
                            content=content,
                            priority=priority,
                            scheduled_at=scheduled_dt,
                            metadata=metadata,
                        )

                        sent_notifications.append(
                            {
                                "user_id": user.user_id,
                                "channel": channel,
                                "notification_id": send_result["notification_id"],
                                "status": "sent" if not scheduled_dt else "scheduled",
                            }
                        )

                    except Exception as e:
                        failed_notifications.append(
                            {
                                "user_id": user.user_id,
                                "channel": channel,
                                "error": str(e),
                            }
                        )

            # 记录通知历史
            await self._save_notification_history(
                notification_id=notification_id,
                tenant_id=tenant_id,
                notification_type=notification_type,
                title=title,
                content=content,
                channels=channels,
                priority=priority,
                target_users=user_ids,
                sent_count=len(sent_notifications),
                failed_count=len(failed_notifications),
                scheduled_at=scheduled_dt,
                metadata=metadata,
            )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=None,
                action="SEND_USER_NOTIFICATION",
                resource_type="NOTIFICATION",
                resource_id=notification_id,
                details={
                    "notification_type": notification_type,
                    "channels": channels,
                    "target_user_count": len(user_ids),
                    "sent_count": len(sent_notifications),
                    "failed_count": len(failed_notifications),
                },
            )

            return {
                "notification_id": notification_id,
                "total_targets": len(user_ids),
                "valid_users": len(valid_users),
                "invalid_users": invalid_users,
                "sent_count": len(sent_notifications),
                "failed_count": len(failed_notifications),
                "sent_notifications": sent_notifications,
                "failed_notifications": failed_notifications,
                "status": "completed" if not scheduled_dt else "scheduled",
                "sent_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"发送用户通知失败: {str(e)}")

    async def get_user_notification_history(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        notification_type: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        limit: int = 50,
    ) -> Dict[str, Any]:
        """
        查询用户通知历史

        支持多种筛选条件的通知历史查询
        """
        try:
            # 解析时间范围
            start_dt = datetime.fromisoformat(start_date) if start_date else None
            end_dt = datetime.fromisoformat(end_date) if end_date else None

            # 查询通知历史
            notifications = await self._query_notification_history(
                tenant_id=tenant_id,
                user_id=user_id,
                notification_type=notification_type,
                status=status,
                start_date=start_dt,
                end_date=end_dt,
                limit=limit,
            )

            return {
                "tenant_id": tenant_id,
                "user_id": user_id,
                "total_count": len(notifications),
                "notifications": notifications,
                "filters": {
                    "notification_type": notification_type,
                    "status": status,
                    "start_date": start_date,
                    "end_date": end_date,
                },
                "queried_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"查询通知历史失败: {str(e)}")

    async def update_notification_status(
        self,
        tenant_id: str,
        notification_id: str,
        user_id: str,
        status: str,
        read_at: Optional[str] = None,
        action_taken: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        更新通知状态

        用户标记通知为已读、已处理等状态
        """
        try:
            # 验证状态有效性
            valid_statuses = ["unread", "read", "dismissed", "acted"]
            if status not in valid_statuses:
                raise ValidationError(f"无效的通知状态，支持的状态: {valid_statuses}")

            # 验证用户和通知存在性
            user = await self._get_user_by_id(user_id, tenant_id)
            if not user:
                raise NotFoundError("用户不存在")

            notification = await self._get_notification_by_id(notification_id)
            if not notification:
                raise NotFoundError("通知不存在")

            # 解析读取时间
            read_dt = None
            if read_at:
                read_dt = datetime.fromisoformat(read_at)
            elif status in ["read", "dismissed", "acted"]:
                read_dt = datetime.now()

            # 更新通知状态
            update_result = await self._update_notification_status(
                notification_id=notification_id,
                user_id=user_id,
                status=status,
                read_at=read_dt,
                action_taken=action_taken,
            )

            # 记录审计日志
            await self._create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action="UPDATE_NOTIFICATION_STATUS",
                resource_type="NOTIFICATION",
                resource_id=notification_id,
                details={
                    "old_status": update_result.get("old_status"),
                    "new_status": status,
                    "action_taken": action_taken,
                },
            )

            return {
                "notification_id": notification_id,
                "user_id": user_id,
                "old_status": update_result.get("old_status"),
                "new_status": status,
                "read_at": read_dt.isoformat() if read_dt else None,
                "action_taken": action_taken,
                "updated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"更新通知状态失败: {str(e)}")

    # ===== 辅助方法实现 =====

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_user_uniqueness(
        self, tenant_id: str, username: str, email: str, phone: Optional[str] = None
    ):
        """验证用户唯一性"""
        # 检查用户名唯一性
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.username == username,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("用户名已存在", "username", username)

        # 检查邮箱唯一性
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.email == email,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("邮箱已存在", "email", email)

        # 检查手机号唯一性（如果提供）
        if phone:
            stmt = select(self.user_model).where(
                and_(
                    self.user_model.tenant_id == tenant_id,
                    self.user_model.phone == phone,
                    self.user_model.status != "deleted",
                )
            )
            result = await self.session.execute(stmt)
            if result.scalar_one_or_none():
                raise DuplicateResourceError("手机号已存在", "phone", phone)

    async def _validate_password_policy(self, tenant_id: str, password: str):
        """验证密码策略"""
        # 基本密码策略验证
        if len(password) < 8:
            raise ValidationError("密码长度不能少于8位")

        if not any(c.isupper() for c in password):
            raise ValidationError("密码必须包含大写字母")

        if not any(c.islower() for c in password):
            raise ValidationError("密码必须包含小写字母")

        if not any(c.isdigit() for c in password):
            raise ValidationError("密码必须包含数字")

        # TODO: 从租户配置中获取更详细的密码策略

    async def _assign_default_roles(self, user_id: str, tenant_id: str):
        """分配默认角色"""
        # TODO: 查询租户的默认角色配置
        # 暂时跳过角色分配，等角色模块实现后再完善

    async def _generate_activation_token(self, user_id: str) -> str:
        """生成激活令牌"""
        activation_token = f"activate_{uuid.uuid4()}"

        # 存储激活令牌到Redis，有效期24小时
        await self.redis_cache_service.set(
            f"activation_token:{activation_token}",
            {"user_id": user_id, "created_at": datetime.now().isoformat()},
            ttl=86400,
        )

        return activation_token

    async def _send_welcome_email(
        self,
        user_id: str,
        email: str,
        username: str,
        activation_token: Optional[str] = None,
    ):
        """发送欢迎邮件"""
        # TODO: 集成邮件服务
        # 暂时只记录日志
        print(f"发送欢迎邮件到 {email}，用户名: {username}，激活令牌: {activation_token}")

    async def _create_audit_log(
        self,
        tenant_id: str,
        user_id: Optional[str],
        action: str,
        resource_type: str,
        resource_id: str,
        details: Dict[str, Any],
    ):
        """创建审计日志"""
        try:
            audit_log = AuditLogBuilder.create_audit_log(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                result="SUCCESS",
            )
            self.session.add(audit_log)
            await self.session.flush()
        except Exception as e:
            # 审计日志失败不应该影响主业务流程
            print(f"审计日志记录失败: {str(e)}")

    async def _cache_user_info(self, user_id: str, user_info: Dict[str, Any]):
        """缓存用户信息"""
        await self.redis_cache_service.set(f"user_info:{user_id}", user_info, ttl=3600)  # 1小时过期

    async def _get_user_by_id(self, user_id: str, tenant_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.user_id == user_id,
                self.user_model.tenant_id == tenant_id,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _validate_roles_exist(self, tenant_id: str, role_ids: List[str]) -> List[str]:
        """验证角色存在性和有效性"""
        stmt = select(self.role_model).where(
            and_(
                self.role_model.role_id.in_(role_ids),
                self.role_model.tenant_id == tenant_id,
                self.role_model.status == CommonStatus.ACTIVE,
            )
        )
        result = await self.session.execute(stmt)
        valid_roles = result.scalars().all()

        valid_role_ids = [role.role_id for role in valid_roles]
        invalid_roles = [role_id for role_id in role_ids if role_id not in valid_role_ids]

        if invalid_roles:
            raise NotFoundError(f"以下角色不存在或未激活: {invalid_roles}")

        return valid_role_ids

    async def _get_user_existing_roles(self, user_id: str, role_ids: List[str]) -> List[str]:
        """获取用户已拥有的角色"""
        stmt = select(self.user_role_model.role_id).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.user_role_model.role_id.in_(role_ids),
            )
        )
        result = await self.session.execute(stmt)
        return [row[0] for row in result.fetchall()]

    async def _get_user_all_roles(self, user_id: str) -> List[Dict[str, Any]]:
        """获取用户所有有效角色"""
        stmt = (
            select(
                self.user_role_model.role_id,
                self.role_model.role_name,
                self.role_model.role_code,
                self.user_role_model.assigned_at,
                self.user_role_model.assignment_type,
            )
            .join(self.role_model, self.user_role_model.role_id == self.role_model.role_id)
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                )
            )
        )
        result = await self.session.execute(stmt)

        roles = []
        for row in result.fetchall():
            roles.append(
                {
                    "role_id": row[0],
                    "role_name": row[1],
                    "role_code": row[2],
                    "assigned_at": row[3].isoformat() if row[3] else None,
                    "assignment_type": row[4],
                    "expires_at": row[5].isoformat() if row[5] else None,
                }
            )

        return roles

    async def _get_role_info(self, role_id: str) -> Dict[str, Any]:
        """获取角色信息"""
        stmt = select(self.role_model).where(self.role_model.role_id == role_id)
        result = await self.session.execute(stmt)
        role = result.scalar_one_or_none()

        if not role:
            return {"name": "", "code": ""}

        return {
            "name": role.role_name,
            "code": role.role_code,
            "description": getattr(role, "description", ""),
        }

    async def _check_role_dependencies(self, user_id: str, role_ids: List[str]) -> Dict[str, Any]:
        """检查角色依赖关系"""
        # TODO: 实现角色依赖检查逻辑
        # 这里应该检查是否有其他业务依赖这些角色
        # 例如：用户创建的资源、分配的权限等

        return {"has_dependencies": False, "dependencies": []}

    async def _clear_user_permissions_cache(self, user_id: str):
        """清除用户权限缓存"""
        cache_keys = [
            f"user_permissions:{user_id}",
            f"user_roles:{user_id}",
            f"user_info:{user_id}",
        ]

        for key in cache_keys:
            await self.redis_cache_service.delete(key)

    async def _get_user_effective_permissions(
        self,
        user_id: str,
        tenant_id: str,
        resource_type: Optional[str] = None,
        include_inherited: bool = True,
    ) -> Dict[str, Any]:
        """获取用户有效权限"""
        # 查询用户角色
        user_roles = await self._get_user_all_roles(user_id)
        role_ids = [role["role_id"] for role in user_roles]

        if not role_ids:
            return {
                "user_id": user_id,
                "permissions": [],
                "total_count": 0,
                "resource_types": [],
            }

        # 查询角色权限
        stmt = (
            select(
                self.permission_model.permission_id,
                self.permission_model.permission_code,
                self.permission_model.permission_name,
                self.permission_model.resource,
                self.permission_model.action,
                self.role_permission_model.role_id,
            )
            .join(
                self.role_permission_model,
                self.permission_model.permission_id == self.role_permission_model.permission_id,
            )
            .where(
                and_(
                    self.role_permission_model.role_id.in_(role_ids),
                    self.permission_model.status == CommonStatus.ACTIVE,
                )
            )
        )

        if resource_type:
            stmt = stmt.where(self.permission_model.resource == resource_type)

        result = await self.session.execute(stmt)

        permissions = []
        resource_types = set()

        for row in result.fetchall():
            permission_data = {
                "permission_id": row[0],
                "code": row[1],
                "name": row[2],
                "resource_type": row[3],
                "action": row[4],
                "source_role_id": row[5],
            }
            permissions.append(permission_data)
            resource_types.add(row[3])

        return {
            "user_id": user_id,
            "permissions": permissions,
            "total_count": len(permissions),
            "resource_types": list(resource_types),
            "roles": user_roles,
        }

    async def _analyze_permission_sources(
        self, user_id: str, tenant_id: str, permission_code: str
    ) -> List[Dict[str, Any]]:
        """分析权限来源"""
        sources = []

        # 查询通过角色获得的权限
        stmt = (
            select(
                self.role_model.role_id,
                self.role_model.role_name,
                self.role_model.role_code,
                self.user_role_model.assigned_at,
                self.user_role_model.assignment_type,
            )
            .join(
                self.user_role_model,
                self.role_model.role_id == self.user_role_model.role_id,
            )
            .join(
                self.role_permission_model,
                self.role_model.role_id == self.role_permission_model.role_id,
            )
            .join(
                self.permission_model,
                self.role_permission_model.permission_id == self.permission_model.permission_id,
            )
            .where(
                and_(
                    self.user_role_model.user_id == user_id,
                    self.permission_model.permission_code == permission_code,
                    self.permission_model.status == CommonStatus.ACTIVE,
                )
            )
        )

        result = await self.session.execute(stmt)

        for row in result.fetchall():
            sources.append(
                {
                    "source_type": "role",
                    "source_id": row[0],
                    "source_name": row[1],
                    "source_code": row[2],
                    "granted_at": row[3].isoformat() if row[3] else None,
                    "assignment_type": row[4],
                }
            )

        return sources

    async def _get_user_role_history(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50,
    ) -> List[Dict[str, Any]]:
        """获取用户角色历史"""
        # TODO: 实现角色历史查询
        # 这需要一个专门的角色历史表来记录角色分配和移除的历史
        # 目前返回当前角色信息作为示例

        current_roles = await self._get_user_all_roles(user_id)

        history = []
        for role in current_roles:
            history.append(
                {
                    "action": "assign",
                    "role_id": role["role_id"],
                    "role_name": role["role_name"],
                    "role_code": role["role_code"],
                    "timestamp": role["assigned_at"],
                    "assignment_type": role["assignment_type"],
                    "expires_at": role["expires_at"],
                    "operator_id": None,  # TODO: 记录操作者
                    "reason": None,  # TODO: 记录操作原因
                }
            )

        return history[:limit]

    async def _get_user_active_sessions(
        self, user_id: str, status: Optional[str] = None, limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取用户活跃会话"""
        # 从Redis查询会话信息
        pattern = f"session:user:{user_id}:*"
        session_keys = await self.redis_cache_service.redis_cache_service.keys(pattern)

        sessions = []
        for key in session_keys[:limit]:
            session_data = await self.redis_cache_service.get(key)
            if session_data:
                if status is None or session_data.get("status") == status:
                    sessions.append(
                        {
                            "session_id": session_data.get("session_id"),
                            "device_info": session_data.get("device_info", {}),
                            "ip_address": session_data.get("ip_address"),
                            "user_agent": session_data.get("user_agent"),
                            "created_at": session_data.get("created_at"),
                            "last_activity": session_data.get("last_activity"),
                            "status": session_data.get("status", "active"),
                        }
                    )

        return sessions

    async def _get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话信息"""
        session_data = await self.redis_cache_service.get(f"session:{session_id}")
        return session_data

    async def _terminate_session(self, session_id: str, reason: Optional[str] = None):
        """终止会话"""
        # 删除会话数据
        await self.redis_cache_service.delete(f"session:{session_id}")

        # 将令牌加入黑名单
        # TODO: 实现令牌黑名单逻辑

        # 记录会话终止信息
        await self.redis_cache_service.set(
            f"session:terminated:{session_id}",
            {"terminated_at": datetime.now().isoformat(), "reason": reason},
            ttl=86400,  # 保留24小时
        )

    async def _check_user_exists(
        self, tenant_id: str, username: str, email: str, phone: Optional[str] = None
    ):
        """检查用户是否已存在"""
        # 检查用户名
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.username == username,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        user = result.scalar_one_or_none()
        if user:
            return user

        # 检查邮箱
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.email == email,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        user = result.scalar_one_or_none()
        if user:
            return user

        # 检查手机号（如果提供）
        if phone:
            stmt = select(self.user_model).where(
                and_(
                    self.user_model.tenant_id == tenant_id,
                    self.user_model.phone == phone,
                    self.user_model.status != "deleted",
                )
            )
            result = await self.session.execute(stmt)
            user = result.scalar_one_or_none()
            if user:
                return user

        return None

    async def _is_notification_channel_enabled(self, user_id: str, channel: str) -> bool:
        """检查用户是否启用了指定的通知渠道"""
        # TODO: 实现用户通知偏好检查
        # 这里应该查询用户的通知偏好设置
        # 暂时返回True，表示所有渠道都启用
        return True

    async def _send_notification(
        self,
        user,
        channel: str,
        notification_type: str,
        title: str,
        content: str,
        priority: str,
        scheduled_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """发送单个通知"""
        notification_id = f"notif_{uuid.uuid4()}"

        # TODO: 实现真实的通知发送逻辑
        # 这里应该根据channel类型调用相应的发送服务
        if channel == "email":
            # 发送邮件通知
            pass
        elif channel == "sms":
            # 发送短信通知
            pass
        elif channel == "push":
            # 发送推送通知
            pass
        elif channel == "in_app":
            # 发送应用内通知
            pass

        # 暂时返回成功结果
        return {
            "notification_id": notification_id,
            "status": "sent" if not scheduled_at else "scheduled",
            "sent_at": datetime.now().isoformat(),
        }

    async def _save_notification_history(
        self,
        notification_id: str,
        tenant_id: str,
        notification_type: str,
        title: str,
        content: str,
        channels: List[str],
        priority: str,
        target_users: List[str],
        sent_count: int,
        failed_count: int,
        scheduled_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """保存通知历史记录"""
        # TODO: 实现通知历史记录保存
        # 这里应该将通知记录保存到数据库
        notification_record = {
            "notification_id": notification_id,
            "tenant_id": tenant_id,
            "notification_type": notification_type,
            "title": title,
            "content": content,
            "channels": channels,
            "priority": priority,
            "target_users": target_users,
            "sent_count": sent_count,
            "failed_count": failed_count,
            "scheduled_at": scheduled_at.isoformat() if scheduled_at else None,
            "created_at": datetime.now().isoformat(),
            "metadata": metadata or {},
        }

        # 暂时存储到Redis作为临时方案
        await self.redis_cache_service.set(
            f"notification_history:{notification_id}",
            notification_record,
            ttl=86400 * 30,  # 保留30天
        )

    async def _query_notification_history(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        notification_type: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50,
    ) -> List[Dict[str, Any]]:
        """查询通知历史"""
        # TODO: 实现真实的通知历史查询
        # 这里应该从数据库查询通知历史记录

        # 暂时返回示例数据
        return [
            {
                "notification_id": f"notif_{uuid.uuid4()}",
                "notification_type": notification_type or "system",
                "title": "系统通知",
                "content": "这是一条示例通知",
                "channels": ["email", "in_app"],
                "priority": "normal",
                "status": status or "sent",
                "created_at": datetime.now().isoformat(),
                "read_at": None,
                "user_id": user_id,
            }
        ]

    async def _get_notification_by_id(self, notification_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取通知"""
        # TODO: 实现真实的通知查询
        # 这里应该从数据库查询通知记录

        # 先尝试从Redis获取
        notification = await self.redis_cache_service.get(f"notification_history:{notification_id}")
        return notification

    async def _update_notification_status(
        self,
        notification_id: str,
        user_id: str,
        status: str,
        read_at: Optional[datetime] = None,
        action_taken: Optional[str] = None,
    ) -> Dict[str, Any]:
        """更新通知状态"""
        # TODO: 实现真实的通知状态更新
        # 这里应该更新数据库中的通知状态

        # 暂时返回示例结果
        return {"old_status": "unread", "updated": True}

    async def _calculate_user_activity(
        self,
        tenant_id: str,
        user_id: Optional[str],
        start_date: datetime,
        end_date: datetime,
    ) -> Dict[str, Any]:
        """计算用户活跃度"""
        # TODO: 实现真实的活跃度计算逻辑
        # 这里应该查询用户的登录记录、操作记录等

        return {
            "total_logins": 45,
            "unique_days": 15,
            "avg_session_duration": "2h 30m",
            "most_active_day": "Monday",
            "activity_trend": "increasing",
            "last_activity": datetime.now().isoformat(),
        }

    async def _calculate_login_statistics(
        self, tenant_id: str, user_id: Optional[str], start_date: datetime
    ) -> Dict[str, Any]:
        """计算登录统计"""
        # TODO: 实现真实的登录统计逻辑

        return {
            "total_logins": 32,
            "successful_logins": 30,
            "failed_logins": 2,
            "unique_devices": 3,
            "peak_login_hour": "09:00",
            "login_frequency": "daily",
            "geographic_distribution": {"China": 28, "USA": 4},
        }

    async def _export_basic_user_data(
        self, tenant_id: str, user_ids: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """导出基本用户数据"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.status != "deleted",
            )
        )

        if user_ids:
            stmt = stmt.where(self.user_model.user_id.in_(user_ids))

        result = await self.session.execute(stmt)
        users = result.scalars().all()

        data = []
        for user in users:
            data.append(
                {
                    "user_id": user.user_id,
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone,
                    "nickname": user.nickname,
                    "status": user.status,
                    "created_at": (user.created_at.isoformat() if user.created_at else None),
                    "last_login": (user.last_login.isoformat() if user.last_login else None),
                }
            )

        return data

    async def _export_detailed_user_data(
        self, tenant_id: str, user_ids: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """导出详细用户数据"""
        basic_data = await self._export_basic_user_data(tenant_id, user_ids)

        # 为每个用户添加角色和权限信息
        for user_data in basic_data:
            user_id = user_data["user_id"]

            # 获取用户角色
            roles = await self._get_user_all_roles(user_id)
            user_data["roles"] = roles

            # 获取用户权限
            permissions = await self._get_user_effective_permissions(user_id, tenant_id)
            user_data["permissions"] = permissions["permissions"]

        return basic_data

    async def _export_user_audit_logs(
        self, tenant_id: str, user_ids: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """导出用户审计日志"""
        # TODO: 实现审计日志查询
        # 这里应该查询audit_log表中与用户相关的记录

        return [
            {
                "log_id": f"log_{uuid.uuid4()}",
                "user_id": user_ids[0] if user_ids else "sample_user",
                "action": "LOGIN",
                "resource_type": "USER",
                "timestamp": datetime.now().isoformat(),
                "ip_address": "*************",
                "user_agent": "Mozilla/5.0...",
                "result": "success",
            }
        ]

    async def _generate_export_file(
        self, export_id: str, data: List[Dict[str, Any]], format: str
    ) -> Dict[str, Any]:
        """生成导出文件"""
        # TODO: 实现文件生成逻辑
        # 这里应该将数据转换为指定格式并保存到文件系统

        filename = f"user_export_{export_id}.{format}"
        file_size = len(str(data))  # 简化的文件大小计算

        return {
            "filename": filename,
            "file_size": file_size,
            "download_url": f"/api/v1/exports/{export_id}/download",
            "expires_at": (datetime.now() + timedelta(hours=24)).isoformat(),
        }

    # ===== 新增的辅助方法 =====

    async def _get_tenant_by_code(self, tenant_code: str) -> Tenant:
        """根据编码获取租户"""
        stmt = select(self.tenant_model).where(
            and_(
                self.tenant_model.tenant_id == tenant_code,
                self.tenant_model.status == CommonStatus.ACTIVE,
            )
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _verify_code(self, code_id: str, verification_code: str, scene: str):
        """验证验证码"""
        # 从Redis获取验证码
        stored_code_data = await self.redis_cache_service.get(f"verification_code:{code_id}")
        if not stored_code_data:
            raise ValidationError("验证码不存在或已过期")

        if stored_code_data.get("code") != verification_code:
            raise ValidationError("验证码错误")

        if stored_code_data.get("scene") != scene:
            raise ValidationError("验证码使用场景不匹配")

        # 验证成功后删除验证码
        await self.redis_cache_service.delete(f"verification_code:{code_id}")

    async def _send_activation_email(
        self, user_id: str, email: str, username: str, activation_token: str
    ):
        """发送激活邮件"""
        # TODO: 集成邮件服务
        # 暂时只记录日志
        print(f"发送激活邮件到 {email}，用户名: {username}，激活令牌: {activation_token}")

    async def _verify_activation_token(self, activation_token: str) -> Optional[str]:
        """验证激活令牌"""
        token_data = await self.redis_cache_service.get(f"activation_token:{activation_token}")
        if not token_data:
            return None
        return token_data.get("user_id")

    async def _delete_activation_token(self, activation_token: str):
        """删除激活令牌"""
        await self.redis_cache_service.delete(f"activation_token:{activation_token}")

    async def _get_user_by_id_only(self, user_id: str):
        """根据ID获取用户（不限制租户）"""
        stmt = select(self.user_model).where(
            and_(self.user_model.user_id == user_id, self.user_model.status != "deleted")
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_users_roles_batch(self, user_ids: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """批量获取用户角色"""
        if not user_ids:
            return {}

        # 查询用户角色关联
        stmt = (
            select(
                self.user_role_model.user_id,
                self.user_role_model.role_id,
                self.user_role_model.assigned_at,
                self.user_role_model.assignment_type,
                self.user_role_model.expiry_date,
                self.role_model.role_name,
                self.role_model.role_code,
            )
            .join(self.role_model, self.user_role_model.role_id == self.role_model.role_id)
            .where(
                and_(
                    self.user_role_model.user_id.in_(user_ids),
                    self.user_role_model.status == CommonStatus.ACTIVE,
                    self.role_model.status == CommonStatus.ACTIVE,
                )
            )
        )

        result = await self.session.execute(stmt)
        rows = result.all()

        # 组织数据
        user_roles_map = {}
        for row in rows:
            user_id = row.user_id
            if user_id not in user_roles_map:
                user_roles_map[user_id] = []

            user_roles_map[user_id].append(
                {
                    "role_id": row.role_id,
                    "role_name": row.role_name,
                    "role_code": row.role_code,
                    "assigned_at": (row.assigned_at.isoformat() if row.assigned_at else None),
                    "assignment_type": row.assignment_type,
                    "expires_at": (row.expiry_date.isoformat() if row.expiry_date else None),
                }
            )

        return user_roles_map

    async def _get_user_security_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户安全信息"""
        # TODO: 实现从安全相关表查询
        # 暂时返回默认值
        return {
            "mfa_enabled": False,
            "password_changed_at": (datetime.now() - timedelta(days=30)).isoformat(),
            "password_expires_at": (datetime.now() + timedelta(days=90)).isoformat(),
            "active_sessions": 0,
        }

    async def _get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """获取用户偏好设置"""
        # 从Redis获取用户偏好
        preferences = await self.redis_cache_service.get(f"user_preferences:{user_id}")
        if preferences:
            return preferences

        # 返回默认偏好设置
        default_preferences = {
            "language": "zh-CN",
            "timezone": "Asia/Shanghai",
            "theme": "light",
            "notifications": {"email": True, "sms": False, "push": True},
            "dashboard_layout": "compact",
        }

        # 缓存默认偏好
        await self.redis_cache_service.set(
            f"user_preferences:{user_id}", default_preferences, ttl=3600
        )
        return default_preferences

    async def _check_email_uniqueness(self, tenant_id: str, email: str, exclude_user_id: str):
        """检查邮箱唯一性（排除指定用户）"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.email == email,
                self.user_model.user_id != exclude_user_id,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("邮箱已存在", "email", email)

    async def _check_phone_uniqueness(self, tenant_id: str, phone: str, exclude_user_id: str):
        """检查手机号唯一性（排除指定用户）"""
        stmt = select(self.user_model).where(
            and_(
                self.user_model.tenant_id == tenant_id,
                self.user_model.phone == phone,
                self.user_model.user_id != exclude_user_id,
                self.user_model.status != "deleted",
            )
        )
        result = await self.session.execute(stmt)
        if result.scalar_one_or_none():
            raise DuplicateResourceError("手机号已存在", "phone", phone)

    async def _validate_status_transition(self, current_status: str, new_status: str):
        """验证状态转换的合法性"""
        allowed_transitions = self.STATUS_TRANSITIONS.get(current_status, [])
        if new_status not in allowed_transitions:
            raise ValidationError(f"不允许从状态 {current_status} 转换到 {new_status}")

    async def _clear_user_cache(self, user_id: str):
        """清理用户相关缓存"""
        cache_keys = [
            f"user_info:{user_id}",
            f"user_permissions:{user_id}:*",
            f"user_preferences:{user_id}",
        ]
        for key in cache_keys:
            if "*" in key:
                # 使用模式删除
                keys = await self.redis_cache_service.redis_cache_service.keys(key)
                if keys:
                    await self.redis_cache_service.delete(*keys)
            else:
                await self.redis_cache_service.delete(key)

    async def _validate_preferences(self, preferences: Dict[str, Any]):
        """验证偏好设置的有效性"""
        # 验证语言设置
        if "language" in preferences:
            valid_languages = ["zh-CN", "en-US", "ja-JP"]
            if preferences["language"] not in valid_languages:
                raise ValidationError(f"不支持的语言设置: {preferences['language']}")

        # 验证时区设置
        if "timezone" in preferences:
            valid_timezones = ["Asia/Shanghai", "UTC", "America/New_York"]
            if preferences["timezone"] not in valid_timezones:
                raise ValidationError(f"不支持的时区设置: {preferences['timezone']}")

        # 验证主题设置
        if "theme" in preferences:
            valid_themes = ["light", "dark", "auto"]
            if preferences["theme"] not in valid_themes:
                raise ValidationError(f"不支持的主题设置: {preferences['theme']}")

    async def _send_status_change_notification(
        self,
        user_id: str,
        old_status: str,
        new_status: str,
        reason: Optional[str] = None,
    ):
        """发送状态变更通知"""
        # TODO: 实现状态变更通知
        print(f"用户 {user_id} 状态从 {old_status} 变更为 {new_status}，原因: {reason}")

    def _generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        import random

        return f"{random.randint(100000, 999999)}"

    async def _check_send_frequency_limit(self, target: str, channel: str, scene: str):
        """检查发送频率限制"""
        # 检查1分钟内发送次数
        minute_key = f"send_freq:{channel}:{target}:minute:{datetime.now().strftime('%Y%m%d%H%M')}"
        minute_count = await self.redis_cache_service.get(minute_key) or 0
        if int(minute_count) >= 1:
            raise ValidationError("发送过于频繁，请稍后再试")

        # 检查1小时内发送次数
        hour_key = f"send_freq:{channel}:{target}:hour:{datetime.now().strftime('%Y%m%d%H')}"
        hour_count = await self.redis_cache_service.get(hour_key) or 0
        if int(hour_count) >= 5:
            raise ValidationError("发送次数过多，请稍后再试")

    async def _record_send_frequency(self, target: str, channel: str, scene: str):
        """记录发送频率"""
        # 记录1分钟内发送次数
        minute_key = f"send_freq:{channel}:{target}:minute:{datetime.now().strftime('%Y%m%d%H%M')}"
        await self.redis_cache_service.redis_cache_service.incr(minute_key)
        await self.redis_cache_service.redis_cache_service.expire(minute_key, 60)

        # 记录1小时内发送次数
        hour_key = f"send_freq:{channel}:{target}:hour:{datetime.now().strftime('%Y%m%d%H')}"
        await self.redis_cache_service.redis_cache_service.incr(hour_key)
        await self.redis_cache_service.redis_cache_service.expire(hour_key, 3600)

    async def _send_sms(self, phone: str, code: str, scene: str):
        """发送短信"""
        # TODO: 集成短信服务
        print(f"发送短信验证码到 {phone}，验证码: {code}，场景: {scene}")

    async def _send_email(self, email: str, code: str, scene: str):
        """发送邮件"""
        # TODO: 集成邮件服务
        print(f"发送邮件验证码到 {email}，验证码: {code}，场景: {scene}")

    async def _check_user_dependencies(self, user_id: str) -> Dict[str, Any]:
        """检查用户依赖关系"""
        # TODO: 实现用户依赖检查逻辑
        # 这里应该检查用户创建的内容、分配的权限等
        # 例如：文档、知识库、任务等

        dependencies = []

        # 检查用户创建的文档数量
        # document_count = await self._count_user_documents(user_id)
        # if document_count > 0:
        #     dependencies.append(f"用户创建了 {document_count} 个文档")

        # 检查用户管理的知识库
        # kb_count = await self._count_user_knowledge_bases(user_id)
        # if kb_count > 0:
        #     dependencies.append(f"用户管理 {kb_count} 个知识库")

        return {"has_dependencies": len(dependencies) > 0, "dependencies": dependencies}

    async def _remove_all_user_roles(self, user_id: str, tenant_id: str):
        """移除用户的所有角色"""
        # 查询用户的所有角色
        stmt = select(self.user_role_model).where(
            and_(
                self.user_role_model.user_id == user_id,
                self.user_role_model.tenant_id == tenant_id,
            )
        )
        result = await self.session.execute(stmt)
        user_roles = result.scalars().all()

        # 删除所有角色关联
        for user_role in user_roles:
            await self.session.delete(user_role)

    async def _transfer_user_data(self, from_user_id: str, to_user_id: str):
        """转移用户数据"""
        # TODO: 实现用户数据转移逻辑
        # 这里应该将用户创建的内容转移给其他用户
        # 例如：文档所有权、知识库管理权等
        print(f"将用户 {from_user_id} 的数据转移给用户 {to_user_id}")
