"""
系统服务

提供系统监控、指标查询、日志管理等功能
"""

import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

import psutil
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from commonlib.exceptions.exceptions import BusinessError, NotFoundError, ValidationError
from commonlib.storages.persistence.redis.repository import RedisRepository
from domain_common.models.iam_models import (
    AuditLog,
    CacheKey,
    SecurityEvent,
    SecurityPolicy,
    SystemConfig,
    Tenant,
    User,
)


class SystemService:
    """系统服务类"""

    # 支持的指标类型
    METRIC_TYPES = [
        "system",
        "database",
        "redis",
        "api",
        "sessions",
        "tenants",
        "users",
        "storage",
        "performance",
    ]

    # 支持的时间范围
    TIME_RANGES = {
        "1h": timedelta(hours=1),
        "6h": timedelta(hours=6),
        "24h": timedelta(hours=24),
        "7d": timedelta(days=7),
        "30d": timedelta(days=30),
    }

    # 日志级别
    LOG_LEVELS = ["DEBUG", "INFO", "WARN", "ERROR", "FATAL"]

    def __init__(
        self,
        session: AsyncSession,
        redis_repo: RedisRepository,
        user_model=User,
        tenant_model=Tenant,
        audit_log_model=AuditLog,
        system_config_model=SystemConfig,
        cache_key_model=CacheKey,
        security_policy_model=SecurityPolicy,
        security_event_model=SecurityEvent,
    ):
        self.session = session
        self.redis_repo = redis_repo
        self.user_model = user_model
        self.tenant_model = tenant_model
        self.audit_log_model = audit_log_model
        self.system_config_model = system_config_model
        self.cache_key_model = cache_key_model
        self.security_policy_model = security_policy_model
        self.security_event_model = security_event_model

    async def query_metrics(
        self,
        tenant_id: Optional[str] = None,
        metric_types: Optional[List[str]] = None,
        time_range: str = "1h",
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询系统指标

        支持系统级和租户级指标查询
        """
        try:
            # 验证时间范围
            if time_range not in self.TIME_RANGES:
                raise ValidationError(f"不支持的时间范围: {time_range}")

            # 验证指标类型
            if metric_types:
                invalid_types = set(metric_types) - set(self.METRIC_TYPES)
                if invalid_types:
                    raise ValidationError(f"不支持的指标类型: {invalid_types}")

            # 计算时间范围
            end_dt = datetime.utcnow()
            if end_time:
                end_dt = datetime.fromisoformat(end_time)

            start_dt = end_dt - self.TIME_RANGES[time_range]
            if start_time:
                start_dt = datetime.fromisoformat(start_time)

            # 获取系统指标
            system_metrics = await self._get_system_metrics()

            # 获取租户指标
            tenant_metrics = None
            if tenant_id:
                tenant_metrics = [await self._get_tenant_metrics(tenant_id)]
            elif not metric_types or "tenants" in metric_types:
                tenant_metrics = await self._get_all_tenant_metrics()

            return {
                "system_metrics": system_metrics,
                "tenant_metrics": tenant_metrics,
                "timestamp": datetime.utcnow().isoformat(),
                "time_range": {
                    "start": start_dt.isoformat(),
                    "end": end_dt.isoformat(),
                    "duration": time_range,
                },
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"查询系统指标失败: {str(e)}")

    async def query_logs(
        self,
        tenant_id: Optional[str] = None,
        level: Optional[str] = None,
        service: Optional[str] = None,
        cursor: Optional[str] = None,
        limit: int = 100,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询系统日志

        支持分页查询和多条件过滤
        """
        try:
            # 验证日志级别
            if level and level not in self.LOG_LEVELS:
                raise ValidationError(f"不支持的日志级别: {level}")

            # 验证分页参数
            if limit > 1000:
                raise ValidationError("每页数量不能超过1000")

            # TODO: 实现真实的日志查询逻辑
            # 这里需要集成日志系统（如ELK、Loki等）
            # 暂时返回模拟数据
            logs = await self._get_mock_logs(
                tenant_id, level, service, cursor, limit, start_time, end_time
            )

            return {
                "logs": logs,
                "total": len(logs),
                "next_cursor": None,
                "has_more": False,
                "query_params": {
                    "tenant_id": tenant_id,
                    "level": level,
                    "service": service,
                    "limit": limit,
                },
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"查询系统日志失败: {str(e)}")

    async def query_audit_logs(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        cursor: Optional[str] = None,
        limit: int = 50,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        查询审计日志

        基于数据库的审计日志查询
        """
        try:
            # 验证分页参数
            if limit > 200:
                raise ValidationError("每页数量不能超过200")

            # 构建查询条件
            conditions = [self.audit_log_model.tenant_id == tenant_id]

            if user_id:
                conditions.append(self.audit_log_model.user_id == user_id)

            if action:
                conditions.append(self.audit_log_model.action == action)

            if resource_type:
                conditions.append(self.audit_log_model.resource_type == resource_type)

            if start_time:
                start_dt = datetime.fromisoformat(start_time)
                conditions.append(self.audit_log_model.created_at >= start_dt)

            if end_time:
                end_dt = datetime.fromisoformat(end_time)
                conditions.append(self.audit_log_model.created_at <= end_dt)

            # 处理游标分页
            if cursor:
                try:
                    cursor_id = int(cursor)
                    conditions.append(self.audit_log_model.id < cursor_id)
                except ValueError:
                    raise ValidationError("无效的游标格式")

            # 查询总数
            count_stmt = select(func.count(self.audit_log_model.id)).where(and_(*conditions))
            total_result = await self.session.execute(count_stmt)
            total = total_result.scalar()

            # 分页查询
            stmt = (
                select(self.audit_log_model)
                .where(and_(*conditions))
                .order_by(desc(self.audit_log_model.created_at))
                .limit(limit + 1)  # 多查一条用于判断是否有更多数据
            )
            result = await self.session.execute(stmt)
            logs = result.scalars().all()

            # 判断是否有更多数据
            has_more = len(logs) > limit
            if has_more:
                logs = logs[:-1]  # 移除多查的一条

            # 构建审计日志列表
            audit_logs = []
            for log in logs:
                # 获取用户名
                username = None
                if log.user_id:
                    user = await self._get_user_by_id(log.user_id)
                    if user:
                        username = user.username

                audit_logs.append(
                    {
                        "log_id": str(log.id),
                        "tenant_id": log.tenant_id,
                        "user_id": log.user_id,
                        "username": username,
                        "action": log.action,
                        "resource_type": log.resource_type,
                        "resource_id": log.resource_id,
                        "details": log.details or {},
                        "ip_address": log.ip_address,
                        "user_agent": log.user_agent,
                        "timestamp": log.created_at.isoformat(),
                    }
                )

            # 生成下一页游标
            next_cursor = None
            if has_more and logs:
                next_cursor = str(logs[-1].id)

            return {
                "audit_logs": audit_logs,
                "total": total,
                "next_cursor": next_cursor,
                "has_more": has_more,
                "query_params": {
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                    "action": action,
                    "resource_type": resource_type,
                    "limit": limit,
                },
            }

        except Exception as e:
            if isinstance(e, (ValidationError, NotFoundError)):
                raise e
            raise BusinessError(f"查询审计日志失败: {str(e)}")

    async def _get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # 获取系统资源使用情况
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")

            # 获取活跃会话数
            active_sessions = await self._get_active_sessions_count()

            # 获取API请求统计
            api_requests_per_minute = await self._get_api_requests_per_minute()

            # 获取数据库连接数
            database_connections = await self._get_database_connections()

            # 获取Redis内存使用情况
            redis_memory_usage = await self._get_redis_memory_usage()

            # 获取错误率和响应时间
            error_rate, response_time_avg = await self._get_performance_metrics()

            return {
                "cpu_usage": round(cpu_usage, 2),
                "memory_usage": round(memory.percent, 2),
                "disk_usage": round(disk.percent, 2),
                "active_sessions": active_sessions,
                "api_requests_per_minute": api_requests_per_minute,
                "database_connections": database_connections,
                "redis_memory_usage": redis_memory_usage,
                "error_rate": error_rate,
                "response_time_avg": response_time_avg,
            }

        except Exception as e:
            # 如果获取系统指标失败，返回默认值
            print(f"获取系统指标失败: {e}")
            return {
                "cpu_usage": 0.0,
                "memory_usage": 0.0,
                "disk_usage": 0.0,
                "active_sessions": 0,
                "api_requests_per_minute": 0,
                "database_connections": 0,
                "redis_memory_usage": 0.0,
                "error_rate": 0.0,
                "response_time_avg": 0.0,
            }

    async def _get_tenant_metrics(self, tenant_id: str) -> Dict[str, Any]:
        """获取单个租户指标"""
        try:
            # 验证租户存在
            tenant = await self._get_tenant_by_id(tenant_id)
            if not tenant:
                raise NotFoundError("租户不存在")

            # 获取用户数量
            user_count_stmt = select(func.count(self.user_model.user_id)).where(
                and_(
                    self.user_model.tenant_id == tenant_id,
                    self.user_model.status != "deleted",
                )
            )
            user_count_result = await self.session.execute(user_count_stmt)
            user_count = user_count_result.scalar() or 0

            # 获取活跃用户数（最近24小时有活动）
            active_users = await self._get_active_users_count(tenant_id)

            # TODO: 获取知识库和文档数量
            # 这里需要集成知识库服务
            knowledge_base_count = 0
            document_count = 0

            # 获取今日API调用次数
            api_calls_today = await self._get_tenant_api_calls_today(tenant_id)

            # TODO: 获取存储使用量
            # 这里需要集成存储服务
            storage_used = 0

            return {
                "tenant_id": tenant_id,
                "user_count": user_count,
                "active_users": active_users,
                "knowledge_base_count": knowledge_base_count,
                "document_count": document_count,
                "api_calls_today": api_calls_today,
                "storage_used": storage_used,
            }

        except Exception as e:
            if isinstance(e, (NotFoundError, ValidationError)):
                raise e
            print(f"获取租户指标失败: {e}")
            return {
                "tenant_id": tenant_id,
                "user_count": 0,
                "active_users": 0,
                "knowledge_base_count": 0,
                "document_count": 0,
                "api_calls_today": 0,
                "storage_used": 0,
            }

    async def _get_all_tenant_metrics(self) -> List[Dict[str, Any]]:
        """获取所有租户指标"""
        try:
            # 获取所有活跃租户
            stmt = (
                select(self.tenant_model).where(self.tenant_model.status == "active").limit(100)
            )  # 限制最多100个租户
            result = await self.session.execute(stmt)
            tenants = result.scalars().all()

            # 并发获取每个租户的指标
            tenant_metrics = []
            for tenant in tenants:
                try:
                    metrics = await self._get_tenant_metrics(tenant.tenant_id)
                    tenant_metrics.append(metrics)
                except Exception as e:
                    print(f"获取租户 {tenant.tenant_id} 指标失败: {e}")
                    continue

            return tenant_metrics

        except Exception as e:
            print(f"获取所有租户指标失败: {e}")
            return []

    async def _get_mock_logs(
        self,
        tenant_id: Optional[str],
        level: Optional[str],
        service: Optional[str],
        cursor: Optional[str],
        limit: int,
        start_time: Optional[str],
        end_time: Optional[str],
    ) -> List[Dict[str, Any]]:
        """获取模拟日志数据"""
        # TODO: 实现真实的日志查询逻辑
        # 这里需要集成日志系统（如ELK、Loki、Fluentd等）

        # 返回模拟日志数据
        mock_logs = [
            {
                "timestamp": datetime.utcnow().isoformat(),
                "level": "INFO",
                "service": "iam_service",
                "message": "用户登录成功",
                "metadata": {
                    "user_id": "user_123",
                    "tenant_id": tenant_id or "tenant_demo",
                    "ip_address": "*************",
                },
            },
            {
                "timestamp": (datetime.utcnow() - timedelta(minutes=5)).isoformat(),
                "level": "WARN",
                "service": "auth_service",
                "message": "登录尝试失败",
                "metadata": {
                    "ip_address": "*************",
                    "reason": "invalid_password",
                },
            },
        ]

        # 根据过滤条件筛选
        filtered_logs = []
        for log in mock_logs:
            if level and log["level"] != level:
                continue
            if service and log["service"] != service:
                continue
            if tenant_id and log["metadata"].get("tenant_id") != tenant_id:
                continue
            filtered_logs.append(log)

        return filtered_logs[:limit]

    async def _get_active_sessions_count(self) -> int:
        """获取活跃会话数"""
        try:
            # 从Redis获取活跃会话数
            session_keys = await self.redis_repo.scan_keys("session:*")
            return len(session_keys)
        except Exception as e:
            print(f"获取活跃会话数失败: {e}")
            return 0

    async def _get_api_requests_per_minute(self) -> int:
        """获取每分钟API请求数"""
        try:
            # 从Redis获取API请求统计
            current_minute = datetime.utcnow().strftime("%Y%m%d%H%M")
            key = f"api_requests:{current_minute}"
            count = await self.redis_repo.get(key)
            return int(count) if count else 0
        except Exception as e:
            print(f"获取API请求数失败: {e}")
            return 0

    async def _get_database_connections(self) -> int:
        """获取数据库连接数"""
        try:
            # TODO: 实现数据库连接数查询
            # 这里需要根据具体的数据库类型实现
            # 例如PostgreSQL: SELECT count(*) FROM pg_stat_activity;
            return 25  # 模拟值
        except Exception as e:
            print(f"获取数据库连接数失败: {e}")
            return 0

    async def _get_redis_memory_usage(self) -> float:
        """获取Redis内存使用率"""
        try:
            # TODO: 实现Redis内存使用率查询
            # 可以通过Redis INFO命令获取
            return 35.8  # 模拟值
        except Exception as e:
            print(f"获取Redis内存使用率失败: {e}")
            return 0.0

    async def _get_performance_metrics(self) -> Tuple[float, float]:
        """获取性能指标（错误率和平均响应时间）"""
        try:
            # TODO: 实现性能指标查询
            # 这里需要集成APM系统或从监控系统获取
            error_rate = 0.02  # 2%错误率
            response_time_avg = 125.5  # 125.5ms平均响应时间
            return error_rate, response_time_avg
        except Exception as e:
            print(f"获取性能指标失败: {e}")
            return 0.0, 0.0

    async def _get_active_users_count(self, tenant_id: str) -> int:
        """获取活跃用户数（最近24小时有活动）"""
        try:
            # 从审计日志中统计最近24小时的活跃用户
            since_time = datetime.utcnow() - timedelta(hours=24)
            stmt = select(func.count(func.distinct(self.audit_log_model.user_id))).where(
                and_(
                    self.audit_log_model.tenant_id == tenant_id,
                    self.audit_log_model.user_id.is_not(None),
                    self.audit_log_model.created_at >= since_time,
                )
            )
            result = await self.session.execute(stmt)
            return result.scalar() or 0
        except Exception as e:
            print(f"获取活跃用户数失败: {e}")
            return 0

    async def _get_tenant_api_calls_today(self, tenant_id: str) -> int:
        """获取租户今日API调用次数"""
        try:
            # 从Redis获取今日API调用统计
            today = datetime.utcnow().strftime("%Y%m%d")
            key = f"api_calls:{tenant_id}:{today}"
            count = await self.redis_repo.get(key)
            return int(count) if count else 0
        except Exception as e:
            print(f"获取租户API调用次数失败: {e}")
            return 0

    async def _get_tenant_by_id(self, tenant_id: str):
        """根据ID获取租户"""
        stmt = select(self.tenant_model).where(self.tenant_model.tenant_id == tenant_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def _get_user_by_id(self, user_id: str):
        """根据ID获取用户"""
        stmt = select(self.user_model).where(self.user_model.user_id == user_id)
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    # ===== 系统配置管理 =====

    async def get_system_config(self, config_key: str) -> Dict[str, Any]:
        """获取系统配置"""
        try:
            stmt = select(self.system_config_model).where(
                self.system_config_model.config_key == config_key
            )
            result = await self.session.execute(stmt)
            config = result.scalar_one_or_none()

            if not config:
                raise NotFoundError(f"配置项不存在: {config_key}")

            return {
                "config_id": config.config_id,
                "config_key": config.config_key,
                "config_value": config.config_value,
                "config_type": config.config_type,
                "description": config.description,
                "is_public": config.is_public,
                "version": config.version,
                "created_at": config.created_at.isoformat(),
                "updated_at": (config.updated_at.isoformat() if config.updated_at else None),
            }

        except Exception as e:
            if isinstance(e, NotFoundError):
                raise e
            raise BusinessError(f"获取系统配置失败: {str(e)}")

    async def set_system_config(
        self,
        config_key: str,
        config_value: Any,
        config_type: str = "string",
        description: Optional[str] = None,
        is_encrypted: bool = False,
        is_public: bool = False,
        version: str = "1.0",
        created_by: Optional[str] = None,
    ) -> Dict[str, Any]:
        """设置系统配置"""
        try:
            # 检查配置是否已存在
            existing_config = await self.session.execute(
                select(self.system_config_model).where(
                    self.system_config_model.config_key == config_key
                )
            )
            config = existing_config.scalar_one_or_none()

            if config:
                # 更新现有配置
                config.config_value = config_value
                config.config_type = config_type
                config.description = description
                config.is_encrypted = is_encrypted
                config.is_public = is_public
                config.version = version
                config.updated_at = datetime.utcnow()
                config.updated_by = created_by
            else:
                # 创建新配置
                config = self.system_config_model(
                    config_id=f"config_{uuid.uuid4()}",
                    config_key=config_key,
                    config_value=config_value,
                    config_type=config_type,
                    description=description,
                    is_encrypted=is_encrypted,
                    is_public=is_public,
                    version=version,
                    created_by=created_by,
                    updated_by=created_by,
                )
                self.session.add(config)

            await self.session.commit()

            return {
                "config_id": config.config_id,
                "config_key": config.config_key,
                "config_value": config.config_value,
                "config_type": config.config_type,
                "description": config.description,
                "is_public": config.is_public,
                "version": config.version,
                "updated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            await self.session.rollback()
            raise BusinessError(f"设置系统配置失败: {str(e)}")

    # ===== 缓存管理 =====

    async def get_cache_statistics(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # 获取缓存键统计
            cache_keys_stmt = select(
                self.cache_key_model.cache_type,
                func.count(self.cache_key_model.id).label("count"),
                func.sum(self.cache_key_model.access_count).label("total_access"),
            ).group_by(self.cache_key_model.cache_type)

            result = await self.session.execute(cache_keys_stmt)
            cache_stats = result.all()

            # 获取Redis统计信息
            redis_info = await self._get_redis_info()

            return {
                "cache_types": [
                    {
                        "type": row.cache_type,
                        "key_count": row.count,
                        "total_access": row.total_access or 0,
                    }
                    for row in cache_stats
                ],
                "redis_info": redis_info,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"获取缓存统计失败: {str(e)}")

    async def clear_cache(
        self, cache_type: Optional[str] = None, pattern: Optional[str] = None
    ) -> Dict[str, Any]:
        """清除缓存"""
        try:
            cleared_count = 0

            if pattern:
                # 按模式清除
                keys = await self.redis_repo.scan_keys(pattern)
                if keys:
                    await self.redis_repo.delete(*keys)
                    cleared_count = len(keys)
            elif cache_type:
                # 按类型清除
                # TODO: 实现按缓存类型清除逻辑
                # 需要从cache_keys表获取对应类型的所有键
                pass
            else:
                # 清除所有缓存
                await self.redis_repo.flushdb()
                cleared_count = -1  # 表示全部清除

            return {
                "cleared_count": cleared_count,
                "cache_type": cache_type,
                "pattern": pattern,
                "cleared_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            raise BusinessError(f"清除缓存失败: {str(e)}")

    async def _get_redis_info(self) -> Dict[str, Any]:
        """获取Redis信息"""
        try:
            # TODO: 实现Redis信息获取
            # 可以通过Redis INFO命令获取详细信息
            return {
                "version": "7.0.0",
                "memory_used": "256MB",
                "memory_peak": "512MB",
                "connected_clients": 10,
                "total_commands_processed": 1000000,
                "keyspace_hits": 950000,
                "keyspace_misses": 50000,
                "hit_rate": 95.0,
            }
        except Exception as e:
            print(f"获取Redis信息失败: {e}")
            return {}
