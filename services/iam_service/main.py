"""
IAM 服务应用入口

基于项目统一的 DI 容器设计，提供用户与权限管理服务
"""

from bootstrap import runtime, setup_bootstrap
from commonlib.configs.basic_configs import BasicConfig
from config.service_config import IAMAppConfig
from fastapi import FastAPI
from routes import api_router

from domain_common.app_builder.default_app_builder import AppBuilder

# 全局配置与服务容器
config, services = setup_bootstrap(role="api")


async def lifespan(local_app: FastAPI):
    """增强的生命周期管理（使用bootstrap runtime）"""
    async with runtime(config, services, role="api"):
        yield


def create_app(app_config: IAMAppConfig) -> FastAPI:
    """优化后的应用工厂"""
    basic_config: BasicConfig= app_config.basic_config
    app_info = basic_config.application

    local_app = FastAPI(
        debug=app_info.debug,
        title=app_info.title,
        description=app_info.description,
        docs_url=app_info.docs_url,
        openapi_url=app_info.openapi_url,
        redoc_url=app_info.redoc_url,
        lifespan=lifespan,
    )

    # 配置应用
    AppBuilder.register_exception_handlers(local_app, basic_config)
    AppBuilder.setup_middlewares(local_app, basic_config)
    AppBuilder.register_health_router(local_app)

    # 注册路由
    local_app.include_router(api_router)

    return local_app


# 创建应用实例
app = create_app(config)

if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8089, log_config=None)
