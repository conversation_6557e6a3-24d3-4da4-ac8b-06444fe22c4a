"""
IAM服务业务配置

包含IAM服务特有的业务逻辑配置，如主题名称、队列名称等
"""

from pydantic import BaseModel, Field


class IAMMessageConfig(BaseModel):
    """IAM消息配置"""

    # 事件主题配置
    user_events_topic: str = Field(default="iam.user.events", description="用户事件主题")

    role_events_topic: str = Field(default="iam.role.events", description="角色事件主题")

    permission_events_topic: str = Field(
        default="iam.permission.events", description="权限事件主题"
    )

    audit_events_topic: str = Field(default="iam.audit.events", description="审计事件主题")

    # 队列配置
    notification_queue: str = Field(default="iam.notifications", description="通知队列")

    cleanup_queue: str = Field(default="iam.cleanup", description="清理任务队列")


class IAMWorkerConfig(BaseModel):
    """IAM Worker配置"""

    # 消费者组配置
    user_consumer_group: str = Field(default="iam-user-workers", description="用户事件消费者组")

    audit_consumer_group: str = Field(default="iam-audit-workers", description="审计事件消费者组")

    # 处理器配置
    batch_size: int = Field(default=10, description="批处理大小")

    processing_timeout: int = Field(default=30, description="消息处理超时时间（秒）")


class IAMBusinessConfig(BaseModel):
    """IAM业务配置"""

    messages: IAMMessageConfig = Field(default_factory=IAMMessageConfig, description="消息配置")

    worker: IAMWorkerConfig = Field(default_factory=IAMWorkerConfig, description="Worker配置")
