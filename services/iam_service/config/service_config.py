"""
IAM服务配置扩展

在通用配置基础上，添加IAM服务特有的业务配置
"""

from typing import Optional

from pydantic import BaseModel, Field

from commonlib.configs.basic_configs import BasicConfig
from commonlib.core.app_config import AppConfig


class IAMMessageTopics(BaseModel):
    """IAM消息主题配置"""

    user_events: str = Field(default="iam.user.events", description="用户事件主题")
    role_events: str = Field(default="iam.role.events", description="角色事件主题")
    permission_events: str = Field(default="iam.permission.events", description="权限事件主题")
    audit_events: str = Field(default="iam.audit.events", description="审计事件主题")


class IAMQueues(BaseModel):
    """IAM队列配置"""

    notifications: str = Field(default="iam.notifications", description="通知队列")
    cleanup: str = Field(default="iam.cleanup", description="清理任务队列")
    batch_processing: str = Field(default="iam.batch", description="批处理队列")


class IAMWorkerSettings(BaseModel):
    """IAM Worker设置"""

    # 消费者组
    user_consumer_group: str = Field(default="iam-user-workers", description="用户事件消费者组")
    audit_consumer_group: str = Field(default="iam-audit-workers", description="审计事件消费者组")

    # 处理配置
    batch_size: int = Field(default=10, description="批处理大小")
    processing_timeout: int = Field(default=30, description="消息处理超时时间（秒）")


class IAMServiceConfig(BaseModel):
    """IAM服务配置"""

    topics: IAMMessageTopics = Field(default_factory=IAMMessageTopics)
    queues: IAMQueues = Field(default_factory=IAMQueues)
    worker: IAMWorkerSettings = Field(default_factory=IAMWorkerSettings)


class IAMAppConfig:
    """IAM应用配置包装器

    在通用AppConfig基础上添加IAM特有的业务配置
    """

    def __init__(self, base_config: AppConfig, iam_config: Optional[IAMServiceConfig] = None):
        self.base_config = base_config
        self.iam_config = iam_config or IAMServiceConfig()

    def __getattr__(self, name):
        """代理到基础配置"""
        return getattr(self.base_config, name)

    @property
    def basic_config(self) -> BasicConfig:
        """获取IAM主题配置"""
        return self.base_config.settings


    @property
    def topics(self) -> IAMMessageTopics:
        """获取IAM主题配置"""
        return self.iam_config.topics

    @property
    def queues(self) -> IAMQueues:
        """获取IAM队列配置"""
        return self.iam_config.queues

    @property
    def iam_worker_config(self) -> IAMWorkerSettings:
        """获取IAM Worker配置"""
        return self.iam_config.worker


def create_iam_config(base_config: AppConfig) -> IAMAppConfig:
    """创建IAM应用配置

    Args:
        base_config: 基础应用配置

    Returns:
        IAM应用配置实例
    """
    return IAMAppConfig(base_config)
