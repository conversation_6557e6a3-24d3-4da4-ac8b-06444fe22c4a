"""
角色管理路由

提供角色的创建、查询、更新、删除等接口
"""

from typing import List, Optional

from container import ServiceContainer
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from services.role_service import RoleService

router = APIRouter()


# ===== 请求模型 =====
class CreateRoleRequest(BaseModel):
    """创建角色请求"""

    role_name: str = Field(
        ...,
        description="角色名称",
        min_length=2,
        max_length=100,
        examples=["管理员", "普通用户"],
    )
    role_code: str = Field(
        ...,
        description="角色编码",
        min_length=2,
        max_length=50,
        examples=["admin", "user"],
    )
    description: Optional[str] = Field(
        None, description="角色描述", examples=["系统管理员角色", "普通用户角色"]
    )
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    permission_ids: Optional[List[str]] = Field(
        [], description="权限ID列表", examples=[["perm_1", "perm_2"], ["perm_3"]]
    )


class ListRolesRequest(BaseModel):
    """查询角色列表请求"""

    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    limit: int = Field(20, description="每页数量", ge=1, le=100, examples=[10, 20, 50])
    search: Optional[str] = Field(None, description="搜索关键词", examples=["管理", "用户"])
    status: Optional[str] = Field(None, description="角色状态筛选", examples=["active", "inactive"])


class GetRoleRequest(BaseModel):
    """获取角色详情请求"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )


class UpdateRoleRequest(BaseModel):
    """更新角色请求"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    role_name: Optional[str] = Field(
        None, description="角色名称", examples=["超级管理员", "普通用户"]
    )
    description: Optional[str] = Field(
        None, description="角色描述", examples=["更新后的角色描述", "新的角色描述"]
    )
    status: Optional[str] = Field(None, description="角色状态", examples=["active", "inactive"])


class DeleteRoleRequest(BaseModel):
    """删除角色请求"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    force: bool = Field(False, description="是否强制删除", examples=[True, False])


class AssignPermissionsRequest(BaseModel):
    """分配权限请求"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    permission_ids: List[str] = Field(
        ...,
        description="权限ID列表",
        examples=[["perm_1", "perm_2"], ["perm_3", "perm_4", "perm_5"]],
    )


class RemovePermissionsRequest(BaseModel):
    """移除权限请求"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    permission_ids: List[str] = Field(
        ...,
        description="要移除的权限ID列表",
        examples=[["perm_1"], ["perm_2", "perm_3"]],
    )


# ===== 响应数据模型 =====
class RoleResponse(BaseModel):
    """角色响应数据"""

    role_id: str = Field(..., description="角色ID", examples=["role_123", "role_456"])
    role_name: str = Field(..., description="角色名称", examples=["管理员", "普通用户"])
    role_code: str = Field(..., description="角色编码", examples=["admin", "user"])
    description: Optional[str] = Field(
        None, description="角色描述", examples=["系统管理员角色", "普通用户角色"]
    )
    status: str = Field(..., description="角色状态", examples=["active", "inactive"])
    tenant_id: str = Field(
        ..., description="租户ID", examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"]
    )
    permissions: List[dict] = Field(
        [],
        description="角色权限列表",
        examples=[
            [
                {"perm_id": "perm_1", "perm_name": "查看用户"},
                {"perm_id": "perm_2", "perm_name": "编辑用户"},
            ]
        ],
    )
    user_count: int = Field(0, description="拥有此角色的用户数量", examples=[5, 10])
    created_at: str = Field(
        ...,
        description="创建时间",
        examples=["2023-01-01T00:00:00Z", "2023-06-15T12:30:45Z"],
    )
    updated_at: str = Field(
        ...,
        description="更新时间",
        examples=["2023-01-10T00:00:00Z", "2023-06-20T12:30:45Z"],
    )


class RoleListResponse(BaseModel):
    """角色列表响应数据"""

    roles: List[RoleResponse] = Field(
        ...,
        description="角色列表",
        examples=[
            [
                {
                    "role_id": "role_123",
                    "role_name": "管理员",
                    "role_code": "admin",
                    "description": "系统管理员角色",
                    "status": "active",
                    "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                    "permissions": [
                        {"perm_id": "perm_1", "perm_name": "查看用户"},
                        {"perm_id": "perm_2", "perm_name": "编辑用户"},
                    ],
                    "user_count": 5,
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-10T00:00:00Z",
                },
                {
                    "role_id": "role_456",
                    "role_name": "普通用户",
                    "role_code": "user",
                    "description": "普通用户角色",
                    "status": "active",
                    "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                    "permissions": [{"perm_id": "perm_1", "perm_name": "查看用户"}],
                    "user_count": 100,
                    "created_at": "2023-01-01T00:00:00Z",
                    "updated_at": "2023-01-10T00:00:00Z",
                },
            ]
        ],
    )
    total: int = Field(..., description="总数量", examples=[2, 10, 100])


class RoleOperationResponse(BaseModel):
    """角色操作响应数据"""

    success: bool = Field(True, description="操作是否成功", examples=[True, False])
    role_id: Optional[str] = Field(None, description="角色ID", examples=["role_123", "role_456"])


# ===== 响应模型 =====
class RoleResponseModel(SuccessResponse[RoleResponse]):
    """角色响应模型"""

    data: RoleResponse


class RoleListResponseModel(SuccessResponse[RoleListResponse]):
    """角色列表响应模型"""

    data: RoleListResponse


class RoleOperationResponseModel(SuccessResponse[RoleOperationResponse]):
    """角色操作响应模型"""

    data: RoleOperationResponse


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建角色",
    description="""
    在指定租户下创建新角色

    **功能说明：**
    - 创建新的角色并设置基本信息
    - 支持同时分配权限给角色
    - 自动验证角色名称和编码的唯一性
    - 记录角色创建审计日志
    - 缓存角色基本信息

    **业务规则：**
    - 角色名称在租户内必须唯一
    - 角色编码在租户内必须唯一
    - 权限ID必须存在且有效
    - 需要有角色管理权限

    **返回数据：**
    - 角色基本信息
    - 分配的权限列表
    - 创建时间和状态
    """,
    response_model=RoleResponseModel,
    responses={
        200: {
            "description": "角色创建成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "角色创建成功",
                        "data": {
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "role_name": "系统管理员",
                            "role_code": "ADMIN",
                            "description": "系统管理员角色",
                            "tenant_id": "tenant_550e8400-e29b-41d4-a716-446655440000",
                            "status": "active",
                            "level": 1,
                            "permissions": [
                                {
                                    "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                                    "permission_name": "用户管理",
                                    "permission_code": "user:manage",
                                    "resource": "user",
                                    "action": "manage",
                                }
                            ],
                            "user_count": 0,
                            "created_at": "2025-01-15T10:30:45.123456",
                            "updated_at": "2025-01-15T10:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "租户不存在"},
        409: {"description": "角色名称或编码已存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色管理"],
)
@inject
async def create_role(
    request: BaseRequest[CreateRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """创建角色"""
    create_role_params = request.data
    result = await role_service.create_role(
        role_name=create_role_params.role_name,
        role_code=create_role_params.role_code,
        description=create_role_params.description,
        tenant_id=create_role_params.tenant_id,
        permission_ids=create_role_params.permission_ids,
    )
    return success_response(result, message="角色创建成功")


@router.post(
    "/list",
    summary="查询角色列表",
    description="""
    分页查询租户下的角色列表，支持搜索和筛选

    **功能说明：**
    - 分页查询角色列表
    - 支持按角色名称、编码、描述搜索
    - 支持按状态筛选
    - 返回角色统计信息（用户数、权限数）
    - 按层级和创建时间排序

    **业务规则：**
    - 只能查询当前租户下的角色
    - 不显示已删除的角色
    - 需要有角色查看权限

    **返回数据：**
    - 角色列表（包含统计信息）
    - 总数量
    - 分页信息
    """,
    response_model=RoleListResponseModel,
    responses={
        200: {
            "description": "查询成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "查询成功",
                        "data": {
                            "roles": [
                                {
                                    "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                                    "role_name": "系统管理员",
                                    "role_code": "ADMIN",
                                    "description": "系统管理员角色",
                                    "level": 1,
                                    "parent_role_id": None,
                                    "status": "active",
                                    "user_count": 2,
                                    "permission_count": 15,
                                    "max_users": 0,
                                    "meta_data": {},
                                    "created_at": "2025-01-15T10:30:45.123456",
                                    "updated_at": "2025-01-15T10:30:45.123456",
                                }
                            ],
                            "total": 1,
                            "limit": 20,
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色管理"],
)
@inject
async def list_roles(
    request: BaseRequest[ListRolesRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """查询角色列表"""
    list_roles_params = request.data
    result = await role_service.list_roles(
        tenant_id=list_roles_params.tenant_id,
        limit=list_roles_params.limit,
        search=list_roles_params.search,
        status=list_roles_params.status,
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取角色详情",
    description="""
    获取指定角色的详细信息，包括权限列表

    **功能说明：**
    - 获取角色完整详细信息
    - 包含角色的所有权限列表
    - 显示父子角色关系
    - 提供角色统计信息
    - 显示角色层级和元数据

    **业务规则：**
    - 角色必须存在且未删除
    - 只能查询当前租户下的角色
    - 需要有角色查看权限

    **返回数据：**
    - 角色完整信息
    - 权限列表详情
    - 父子角色关系
    - 统计信息（用户数、权限数）
    """,
    response_model=RoleResponseModel,
    responses={
        200: {
            "description": "查询成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "查询成功",
                        "data": {
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "role_name": "系统管理员",
                            "role_code": "ADMIN",
                            "description": "系统管理员角色",
                            "level": 1,
                            "parent_role_id": None,
                            "parent_role": None,
                            "child_roles": [],
                            "status": "active",
                            "max_users": 0,
                            "meta_data": {},
                            "permissions": [
                                {
                                    "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                                    "permission_name": "用户管理",
                                    "permission_code": "user:manage",
                                    "resource": "user",
                                    "action": "manage",
                                    "description": "管理用户的权限",
                                    "assigned_at": "2025-01-15T10:30:45.123456",
                                }
                            ],
                            "statistics": {
                                "user_count": 2,
                                "permission_count": 15,
                                "direct_permission_count": 10,
                                "inherited_permission_count": 5,
                            },
                            "created_at": "2025-01-15T10:30:45.123456",
                            "updated_at": "2025-01-15T10:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "角色不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色管理"],
)
@inject
async def get_role_detail(
    request: BaseRequest[GetRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """获取角色详情"""
    get_role_params = request.data
    result = await role_service.get_role_detail(
        role_id=get_role_params.role_id, tenant_id=get_role_params.tenant_id
    )
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新角色信息",
    description="""
    更新角色的基本信息

    **功能说明：**
    - 更新角色名称、描述、状态等信息
    - 支持部分字段更新
    - 自动验证角色名称唯一性
    - 验证状态转换的合法性
    - 记录角色更新审计日志

    **业务规则：**
    - 角色必须存在且未删除
    - 新角色名称在租户内必须唯一
    - 状态转换必须符合规则
    - 需要有角色管理权限

    **返回数据：**
    - 更新后的角色信息
    - 权限列表
    - 用户数量统计
    - 更新时间
    """,
    response_model=RoleResponseModel,
    responses={
        200: {
            "description": "角色信息更新成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "角色信息更新成功",
                        "data": {
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "role_name": "高级管理员",
                            "role_code": "ADMIN",
                            "description": "更新后的角色描述",
                            "status": "active",
                            "level": 1,
                            "permissions": [
                                {
                                    "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                                    "permission_name": "用户管理",
                                    "permission_code": "user:manage",
                                    "resource": "user",
                                    "action": "manage",
                                }
                            ],
                            "user_count": 2,
                            "created_at": "2025-01-15T10:30:45.123456",
                            "updated_at": "2025-01-15T11:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "角色不存在"},
        409: {"description": "角色名称已存在或状态转换不合法"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色管理"],
)
@inject
async def update_role(
    request: BaseRequest[UpdateRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """更新角色信息"""
    update_role_params = request.data
    result = await role_service.update_role(
        role_id=update_role_params.role_id,
        tenant_id=update_role_params.tenant_id,
        role_name=update_role_params.role_name,
        description=update_role_params.description,
        status=update_role_params.status,
    )
    return success_response(result, message="角色信息更新成功")


@router.post(
    "/delete",
    summary="删除角色",
    description="""
    删除指定角色，支持软删除和强制删除

    **功能说明：**
    - 软删除角色（标记为已删除状态）
    - 支持强制删除模式
    - 自动处理角色依赖关系
    - 清理用户角色关联
    - 移除角色权限关联
    - 处理子角色的父级关系

    **业务规则：**
    - 角色必须存在且未删除
    - 有用户或子角色时需要强制删除
    - 删除后无法恢复（软删除）
    - 需要有角色管理权限

    **返回数据：**
    - 删除操作结果
    - 清理统计信息
    - 删除时间
    """,
    response_model=RoleOperationResponseModel,
    responses={
        200: {
            "description": "角色删除成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "角色删除成功",
                        "data": {
                            "success": True,
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "force": False,
                            "deleted_at": "2025-01-15T10:30:45.123456",
                            "cleanup_summary": {
                                "users_unassigned": 2,
                                "permissions_removed": 15,
                                "child_roles_updated": 1,
                            },
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "角色不存在"},
        409: {"description": "角色存在依赖关系，需要强制删除"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色管理"],
)
@inject
async def delete_role(
    request: BaseRequest[DeleteRoleRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """删除角色"""
    delete_role_params = request.data
    result = await role_service.delete_role(
        role_id=delete_role_params.role_id,
        tenant_id=delete_role_params.tenant_id,
        force=delete_role_params.force,
    )
    return success_response(result, message="角色删除成功")


@router.post(
    "/assign_permissions",
    summary="分配权限",
    description="""
    为角色分配一个或多个权限

    **功能说明：**
    - 批量分配权限给角色
    - 自动验证权限的有效性
    - 检查权限重复分配
    - 记录权限分配审计日志
    - 清理权限缓存

    **业务规则：**
    - 角色必须存在且未删除
    - 权限必须存在且有效
    - 不能重复分配相同权限
    - 需要有权限管理权限

    **返回数据：**
    - 成功分配的权限列表
    - 分配失败的权限及原因
    - 分配时间
    """,
    response_model=RoleOperationResponseModel,
    responses={
        200: {
            "description": "权限分配成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "权限分配成功",
                        "data": {
                            "success": True,
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "assigned_permissions": [
                                {
                                    "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                                    "permission_name": "用户管理",
                                    "permission_code": "user:manage",
                                    "resource": "user",
                                    "action": "manage",
                                    "assigned_at": "2025-01-15T10:30:45.123456",
                                }
                            ],
                            "failed_permissions": [],
                            "assigned_at": "2025-01-15T10:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "角色或权限不存在"},
        409: {"description": "权限已分配"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色权限管理"],
)
@inject
async def assign_permissions(
    request: BaseRequest[AssignPermissionsRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """分配权限"""
    assign_permissions_params = request.data
    result = await role_service.assign_permissions(
        role_id=assign_permissions_params.role_id,
        tenant_id=assign_permissions_params.tenant_id,
        permission_ids=assign_permissions_params.permission_ids,
    )
    return success_response(result, message="权限分配成功")


@router.post(
    "/remove_permissions",
    summary="移除权限",
    description="""
    移除角色的一个或多个权限

    **功能说明：**
    - 批量移除角色的权限
    - 自动验证权限关联关系
    - 检查权限移除的合法性
    - 记录权限移除审计日志
    - 清理权限缓存

    **业务规则：**
    - 角色必须存在且未删除
    - 角色必须拥有要移除的权限
    - 不能移除不存在的权限关联
    - 需要有权限管理权限

    **返回数据：**
    - 成功移除的权限列表
    - 移除失败的权限及原因
    - 移除时间
    """,
    response_model=RoleOperationResponseModel,
    responses={
        200: {
            "description": "权限移除成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "权限移除成功",
                        "data": {
                            "success": True,
                            "role_id": "role_550e8400-e29b-41d4-a716-446655440000",
                            "removed_permissions": [
                                {
                                    "permission_id": "perm_550e8400-e29b-41d4-a716-446655440001",
                                    "permission_name": "用户管理",
                                    "permission_code": "user:manage",
                                    "removed_at": "2025-01-15T10:30:45.123456",
                                }
                            ],
                            "failed_permissions": [],
                            "removed_at": "2025-01-15T10:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "角色不存在或权限未分配"},
        500: {"description": "服务器内部错误"},
    },
    tags=["角色权限管理"],
)
@inject
async def remove_permissions(
    request: BaseRequest[RemovePermissionsRequest],
    role_service: RoleService = Depends(Provide[ServiceContainer.role_service]),
):
    """移除权限"""
    remove_permissions_params = request.data
    result = await role_service.remove_permissions(
        role_id=remove_permissions_params.role_id,
        tenant_id=remove_permissions_params.tenant_id,
        permission_ids=remove_permissions_params.permission_ids,
    )
    return success_response(result, message="权限移除成功")
