"""
租户管理路由

提供租户的创建、查询、更新、删除等接口
"""

from typing import Any, Dict, List, Optional

from container import ServiceContainer
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from services.tenant_service import TenantService

router = APIRouter()


# ===== 请求模型 =====
class CreateTenantRequest(BaseModel):
    """
    创建租户请求模型

    用于创建新的租户组织，包含租户基本信息和配置参数
    """

    tenant_name: str = Field(
        ...,
        description="租户名称，用于显示的组织名称，支持中英文字符",
        min_length=2,
        max_length=100,
        examples=["示例科技有限公司"],
    )
    tenant_code: str = Field(
        ...,
        description="租户编码，全局唯一标识符，只能包含字母、数字、下划线和连字符，创建后不可修改",
        min_length=2,
        max_length=50,
        examples=["DEMO_TECH_CORP"],
    )
    description: Optional[str] = Field(
        None,
        description="租户描述信息，可选字段，用于记录租户的详细说明",
        max_length=500,
        examples=["这是一家专注于人工智能技术的科技公司"],
    )
    settings: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="租户配置信息，JSON格式，包含密码策略、会话超时等配置项",
        examples=[
            {
                "password_policy": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_digits": True,
                    "require_special_chars": True,
                },
                "session_timeout": 7200,
                "max_login_attempts": 5,
                "account_lockout_duration": 1800,
            }
        ],
    )
    max_users: int = Field(
        1000,
        description="租户最大用户数限制，用于控制租户规模，最小值为1",
        ge=1,
        le=100000,
        examples=[1000],
    )
    admin_user: Optional[Dict[str, Any]] = Field(
        None,
        description="默认管理员用户信息，可选字段，如果提供将自动创建管理员账户",
        examples=[
            {
                "username": "admin",
                "email": "<EMAIL>",
                "phone": "***********",
                "password": "Admin123!@#",
                "nickname": "系统管理员",
            }
        ],
    )


class ListTenantsRequest(BaseModel):
    """
    查询租户列表请求模型

    支持分页查询、关键词搜索和状态筛选
    """

    limit: int = Field(20, description="每页返回的记录数量，范围1-100", ge=1, le=100, examples=[20])
    search: Optional[str] = Field(
        None,
        description="搜索关键词，支持按租户名称和租户编码进行模糊搜索",
        max_length=100,
        examples=["科技"],
    )
    status: Optional[str] = Field(
        None,
        description="租户状态筛选，可选值：active(活跃)、inactive(非活跃)、pending(待审核)、suspended(暂停)、deleted(已删除)",
        examples=["active"],
    )


class GetTenantRequest(BaseModel):
    """
    获取租户详情请求模型

    通过租户ID获取指定租户的详细信息
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )


class UpdateTenantRequest(BaseModel):
    """
    更新租户信息请求模型

    支持部分字段更新，只传入需要修改的字段即可
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    tenant_name: Optional[str] = Field(
        None,
        description="租户名称，如需修改则传入新的名称",
        min_length=2,
        max_length=100,
        examples=["更新后的企业名称"],
    )
    description: Optional[str] = Field(
        None,
        description="租户描述信息，如需修改则传入新的描述",
        max_length=500,
        examples=["更新后的企业描述信息"],
    )
    max_users: Optional[int] = Field(
        None,
        description="最大用户数限制，如需修改则传入新的限制值",
        ge=1,
        le=100000,
        examples=[2000],
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="租户配置信息，如需修改则传入新的配置，会完全替换原有配置",
        examples=[
            {
                "password_policy": {"min_length": 10, "require_special_chars": True},
                "session_timeout": 3600,
            }
        ],
    )
    status: Optional[str] = Field(
        None,
        description="租户状态，可选值：active、inactive、pending、suspended",
        examples=["active"],
    )


class DeleteTenantRequest(BaseModel):
    """
    删除租户请求模型

    支持软删除和硬删除两种模式
    """

    tenant_id: str = Field(
        ...,
        description="要删除的租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )


# ================================
# 2.2 租户配置管理相关模型
# ================================


class GetTenantSettingsRequest(BaseModel):
    """
    获取租户配置请求模型
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )


class UpdateTenantSettingsRequest(BaseModel):
    """
    更新租户配置请求模型
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    settings: Dict[str, Any] = Field(
        ...,
        description="租户配置信息，支持部分更新",
        examples=[
            {
                "password_policy": {"min_length": 10, "require_special_chars": True},
                "session_config": {"session_timeout": 3600},
            }
        ],
    )
    merge_mode: str = Field(
        "merge",
        description="配置合并模式：merge（合并）、replace（替换）、patch（深度合并）",
        examples=["merge"],
    )


class ChangeTenantStatusRequest(BaseModel):
    """
    变更租户状态请求模型
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    new_status: str = Field(..., description="新状态值", examples=["active"])
    reason: str = Field("admin_action", description="状态变更原因", examples=["admin_action"])
    comment: Optional[str] = Field(
        None, description="变更说明", max_length=500, examples=["管理员手动激活租户"]
    )


class CheckTenantQuotasRequest(BaseModel):
    """
    检查租户配额请求模型
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )


class AutoManageTenantStatusRequest(BaseModel):
    """
    自动管理租户状态请求模型
    """

    tenant_id: Optional[str] = Field(
        None,
        description="租户ID，如果不指定则处理所有租户",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )


# ===== 响应数据模型 =====
class TenantStatistics(BaseModel):
    """租户统计信息模型"""

    user_count: int = Field(..., description="总用户数量", examples=[25])
    active_user_count: int = Field(..., description="活跃用户数量", examples=[20])
    role_count: int = Field(..., description="角色数量", examples=[5])
    permission_count: int = Field(..., description="权限数量", examples=[15])
    last_updated: str = Field(
        ..., description="统计信息最后更新时间", examples=["2025-01-23T10:30:45.123456"]
    )


class TenantResponse(BaseModel):
    """
    租户基本信息响应数据模型

    包含租户的基础信息和当前状态
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符，UUID格式",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    tenant_name: str = Field(
        ..., description="租户名称，用于显示的组织名称", examples=["示例科技有限公司"]
    )
    tenant_code: str = Field(
        ..., description="租户编码，全局唯一标识符", examples=["DEMO_TECH_CORP"]
    )
    description: Optional[str] = Field(
        None, description="租户描述信息", examples=["专注于人工智能技术的科技公司"]
    )
    status: str = Field(
        ...,
        description="租户当前状态：active(活跃)、inactive(非活跃)、pending(待审核)、suspended(暂停)、deleted(已删除)",
        examples=["active"],
    )
    max_users: int = Field(..., description="租户最大用户数限制", examples=[1000])
    current_users: int = Field(..., description="租户当前用户数量", examples=[25])
    settings: Dict[str, Any] = Field(
        default_factory=dict,
        description="租户配置信息，包含密码策略、会话超时等设置",
        examples=[
            {
                "password_policy": {"min_length": 8, "require_special_chars": True},
                "session_timeout": 3600,
            }
        ],
    )
    created_at: str = Field(
        ...,
        description="租户创建时间，ISO 8601格式",
        examples=["2025-01-23T10:30:45.123456"],
    )
    updated_at: Optional[str] = Field(
        None,
        description="租户最后更新时间，ISO 8601格式",
        examples=["2025-01-23T11:00:00.123456"],
    )


class TenantDetailResponse(TenantResponse):
    """
    租户详细信息响应数据模型

    继承基本租户信息，并添加统计信息
    """

    statistics: TenantStatistics = Field(
        ..., description="租户统计信息，包含用户、角色、权限等数量统计"
    )


class CreateTenantResponse(TenantResponse):
    """
    创建租户响应数据模型

    包含新创建租户的完整信息和初始化结果
    """

    admin_user: Optional[Dict[str, Any]] = Field(
        None,
        description="创建的默认管理员用户信息",
        examples=[
            {
                "user_id": "550e8400-e29b-41d4-a716-446655440001",
                "username": "admin",
                "email": "<EMAIL>",
                "role": "SUPER_ADMIN",
            }
        ],
    )
    default_roles_created: List[str] = Field(
        ...,
        description="自动创建的默认角色列表",
        examples=["SUPER_ADMIN", "ADMIN", "USER"],
    )
    default_permissions_created: int = Field(
        ..., description="自动创建的默认权限数量", examples=[9]
    )


class UpdateTenantResponse(TenantResponse):
    """
    更新租户响应数据模型

    包含更新后的租户信息和变更记录
    """

    changes: Dict[str, Dict[str, Any]] = Field(
        default={},
        description="本次更新的变更记录，记录字段的旧值和新值",
        examples=[
            {
                "tenant_name": {"old": "旧企业名称", "new": "新企业名称"},
                "max_users": {"old": 500, "new": 1000},
            }
        ],
    )


class DeleteTenantResponse(BaseModel):
    """
    删除租户响应数据模型

    包含删除操作的结果和清理摘要
    """

    tenant_id: str = Field(
        ...,
        description="被删除的租户ID",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    delete_type: str = Field(
        ..., description="删除类型：soft(软删除) 或 hard(硬删除)", examples=["soft"]
    )
    status: str = Field(..., description="删除后的租户状态", examples=["deleted"])
    deleted_at: str = Field(
        ...,
        description="删除时间，ISO 8601格式",
        examples=["2025-01-23T10:30:45.123456"],
    )
    cleanup_summary: Dict[str, Any] = Field(
        ...,
        description="数据清理摘要，包含清理的用户、角色、权限等数量",
        examples=[{"users_archived": 25, "roles_removed": 5, "permissions_removed": 15}],
    )
    backup_info: Optional[Dict[str, Any]] = Field(
        None,
        description="备份信息，仅在硬删除且启用备份时返回",
        examples=[
            {
                "backup_id": "backup_550e8400-e29b-41d4-a716-446655440000",
                "backup_time": "2025-01-23T10:30:45.123456",
                "backup_url": "https://backups.examples.com/tenants/backup_550e8400.zip",
                "backup_size": "125MB",
            }
        ],
    )


class TenantListResponse(BaseModel):
    """
    租户列表响应数据模型

    支持分页查询的租户列表数据
    """

    items: List[TenantResponse] = Field(
        ..., description="租户信息列表，包含当前页的所有租户基本信息"
    )
    total: int = Field(..., description="符合查询条件的租户总数量", examples=[100])


# ===== 响应模型 =====
class TenantDetailResponseModel(SuccessResponse[TenantDetailResponse]):
    """
    租户详情响应模型

    用于获取租户详细信息接口的响应
    """

    data: TenantDetailResponse = Field(..., description="租户详细信息数据")


class TenantListResponseModel(SuccessResponse[TenantListResponse]):
    """
    租户列表响应模型

    用于查询租户列表接口的响应
    """

    data: TenantListResponse = Field(..., description="租户列表数据")


class CreateTenantResponseModel(SuccessResponse[CreateTenantResponse]):
    """
    创建租户响应模型

    用于创建租户接口的响应
    """

    data: CreateTenantResponse = Field(..., description="创建租户结果数据")


class UpdateTenantResponseModel(SuccessResponse[UpdateTenantResponse]):
    """
    更新租户响应模型

    用于更新租户信息接口的响应
    """

    data: UpdateTenantResponse = Field(..., description="更新租户结果数据")


class DeleteTenantResponseModel(SuccessResponse[DeleteTenantResponse]):
    """
    删除租户响应模型

    用于删除租户接口的响应
    """

    data: DeleteTenantResponse = Field(..., description="删除租户结果数据")


# ===== 租户配置管理响应模型 =====
class TenantSettingsResponse(BaseModel):
    """
    租户配置响应数据模型

    包含租户的完整配置信息
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    settings: Dict[str, Any] = Field(
        ...,
        description="租户配置信息",
        examples=[
            {
                "password_policy": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_digits": True,
                    "require_special_chars": True,
                },
                "session_config": {
                    "session_timeout": 7200,
                    "max_concurrent_sessions": 5,
                },
                "security_config": {
                    "max_login_attempts": 5,
                    "account_lockout_duration": 1800,
                },
            }
        ],
    )
    last_updated: str = Field(
        ..., description="配置最后更新时间", examples=["2025-01-23T10:30:45.123456"]
    )
    settings_version: str = Field(..., description="配置版本号", examples=["1.0"])
    is_default: bool = Field(..., description="是否为默认配置", examples=[False])


class UpdateTenantSettingsResponse(BaseModel):
    """
    更新租户配置响应数据模型

    包含配置更新的结果信息
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    settings: Dict[str, Any] = Field(
        ...,
        description="更新后的配置信息",
        examples=[{"password_policy": {"min_length": 10, "require_special_chars": True}}],
    )
    merge_mode: str = Field(..., description="使用的合并模式", examples=["merge"])
    updated_keys: List[str] = Field(
        ...,
        description="本次更新的配置键列表",
        examples=[["password_policy", "session_config"]],
    )
    updated_at: str = Field(..., description="更新时间", examples=["2025-01-23T10:30:45.123456"])


class ChangeTenantStatusResponse(BaseModel):
    """
    变更租户状态响应数据模型

    包含状态变更的详细信息
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    old_status: str = Field(..., description="原状态", examples=["pending"])
    new_status: str = Field(..., description="新状态", examples=["active"])
    reason: str = Field(..., description="变更原因", examples=["admin_action"])
    comment: Optional[str] = Field(None, description="变更说明", examples=["管理员手动激活租户"])
    changed_at: str = Field(..., description="变更时间", examples=["2025-01-23T10:30:45.123456"])
    changed_by: Optional[str] = Field(None, description="操作者ID", examples=["admin_user_id"])


class QuotaInfo(BaseModel):
    """配额信息模型"""

    limit: int = Field(..., description="配额限制", examples=[1000])
    current: int = Field(..., description="当前使用量", examples=[25])
    usage_percentage: float = Field(..., description="使用百分比", examples=[2.5])
    exceeded: bool = Field(..., description="是否超限", examples=[False])
    remaining: int = Field(..., description="剩余配额", examples=[975])


class CheckTenantQuotasResponse(BaseModel):
    """
    检查租户配额响应数据模型

    包含配额检查的详细结果
    """

    tenant_id: str = Field(
        ...,
        description="租户唯一标识符",
        examples=["6e4bf872-3aa7-4e32-8d78-dd17b4b03c61"],
    )
    has_violations: bool = Field(..., description="是否存在配额违规", examples=[False])
    violations: List[str] = Field(..., description="配额违规列表", examples=[[]])
    warnings: List[str] = Field(..., description="配额警告列表", examples=[["用户数量接近限制"]])
    quotas: Dict[str, QuotaInfo] = Field(
        ...,
        description="各类配额详细信息",
        examples=[
            {
                "users": {
                    "limit": 1000,
                    "current": 25,
                    "usage_percentage": 2.5,
                    "exceeded": False,
                    "remaining": 975,
                },
                "storage": {
                    "limit": 10737418240,  # 10GB
                    "current": 1073741824,  # 1GB
                    "usage_percentage": 10.0,
                    "exceeded": False,
                    "remaining": 9663676416,
                },
            }
        ],
    )
    checked_at: str = Field(..., description="检查时间", examples=["2025-01-23T10:30:45.123456"])


class StatusChangeRecord(BaseModel):
    """状态变更记录模型"""

    tenant_id: str = Field(..., description="租户ID")
    old_status: str = Field(..., description="原状态")
    new_status: str = Field(..., description="新状态")
    reason: str = Field(..., description="变更原因")
    changed_at: str = Field(..., description="变更时间")


class AutoManageTenantStatusResponse(BaseModel):
    """
    自动管理租户状态响应数据模型

    包含自动管理的处理结果
    """

    processed_tenants: int = Field(..., description="处理的租户数量", examples=[5])
    status_changes: List[StatusChangeRecord] = Field(
        ...,
        description="执行的状态变更列表",
        examples=[
            [
                {
                    "tenant_id": "tenant1",
                    "old_status": "active",
                    "new_status": "suspended",
                    "reason": "quota_exceeded",
                    "changed_at": "2025-01-23T10:30:45.123456",
                }
            ]
        ],
    )
    quota_violations: List[Dict[str, Any]] = Field(
        ..., description="发现的配额违规情况", examples=[[]]
    )
    errors: List[Dict[str, Any]] = Field(..., description="处理过程中的错误信息", examples=[[]])
    processed_at: str = Field(..., description="处理时间", examples=["2025-01-23T10:30:45.123456"])


# ===== 租户配置管理响应模型包装类 =====
class TenantSettingsResponseModel(SuccessResponse[TenantSettingsResponse]):
    """
    租户配置响应模型

    用于获取租户配置接口的响应
    """

    data: TenantSettingsResponse = Field(..., description="租户配置数据")


class UpdateTenantSettingsResponseModel(SuccessResponse[UpdateTenantSettingsResponse]):
    """
    更新租户配置响应模型

    用于更新租户配置接口的响应
    """

    data: UpdateTenantSettingsResponse = Field(..., description="配置更新结果数据")


class ChangeTenantStatusResponseModel(SuccessResponse[ChangeTenantStatusResponse]):
    """
    变更租户状态响应模型

    用于变更租户状态接口的响应
    """

    data: ChangeTenantStatusResponse = Field(..., description="状态变更结果数据")


class CheckTenantQuotasResponseModel(SuccessResponse[CheckTenantQuotasResponse]):
    """
    检查租户配额响应模型

    用于检查租户配额接口的响应
    """

    data: CheckTenantQuotasResponse = Field(..., description="配额检查结果数据")


class AutoManageTenantStatusResponseModel(SuccessResponse[AutoManageTenantStatusResponse]):
    """
    自动管理租户状态响应模型

    用于自动管理租户状态接口的响应
    """

    data: AutoManageTenantStatusResponse = Field(..., description="自动管理结果数据")


# ===== 路由端点 =====
@router.post(
    "/create",
    summary="创建租户",
    description="""
    创建新的租户组织

    **功能说明：**
    - 创建新的租户组织，包含基本信息和配置
    - 自动初始化默认角色体系（超级管理员、管理员、普通用户）
    - 自动创建默认权限体系（系统权限、业务权限）
    - 可选择创建默认管理员用户
    - 支持自定义租户配置（密码策略、会话超时等）

    **业务规则：**
    - 租户编码全局唯一，创建后不可修改
    - 租户名称在系统内唯一
    - 最大用户数限制范围：1-100000
    - 租户编码只能包含字母、数字、下划线和连字符

    **返回数据：**
    - 新创建的租户完整信息
    - 自动创建的默认角色列表
    - 自动创建的权限数量
    - 管理员用户信息（如果创建）
    """,
    response_model=CreateTenantResponseModel,
    responses={
        200: {
            "description": "租户创建成功",
            "content": {
                "application/json": {
                    "examples": {
                        "status": "success",
                        "code": 200,
                        "message": "租户创建成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "tenant_name": "示例科技有限公司",
                            "tenant_code": "DEMO_TECH_CORP",
                            "status": "pending",
                            "max_users": 1000,
                            "current_users": 1,
                            "default_roles_created": ["SUPER_ADMIN", "ADMIN", "USER"],
                            "default_permissions_created": 9,
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        409: {"description": "租户编码或名称已存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户管理"],
)
@inject
async def create_tenant(
    request: BaseRequest[CreateTenantRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    创建租户

    创建新的租户组织，包含完整的初始化流程
    """
    create_tenant_params = request.data
    result = await tenant_service.create_tenant(
        tenant_name=create_tenant_params.tenant_name,
        tenant_code=create_tenant_params.tenant_code,
        description=create_tenant_params.description,
        max_users=create_tenant_params.max_users,
        settings=create_tenant_params.settings,
        admin_user=create_tenant_params.admin_user,
    )
    return success_response(result, message="租户创建成功")


@router.post(
    "/list",
    summary="查询租户列表",
    description="""
    分页查询租户列表，支持搜索和筛选

    **功能说明：**
    - 支持游标分页查询，性能优异
    - 支持按租户名称和编码进行模糊搜索
    - 支持按租户状态进行筛选
    - 返回租户基本信息和统计数据
    - 自动计算每个租户的当前用户数量

    **查询参数：**
    - limit: 每页数量，范围1-100，默认20
    - search: 搜索关键词，支持租户名称和编码模糊匹配
    - status: 状态筛选，可选值：active、inactive、pending、suspended、deleted

    **返回数据：**
    - 租户列表：包含基本信息和用户统计
    """,
    response_model=TenantListResponseModel,
    responses={
        200: {
            "description": "查询成功",
            "content": {
                "application/json": {
                    "examples": {
                        "status": "success",
                        "code": 200,
                        "message": "查询成功",
                        "data": {
                            "tenants": [
                                {
                                    "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                                    "tenant_name": "示例科技有限公司",
                                    "tenant_code": "DEMO_TECH_CORP",
                                    "status": "active",
                                    "max_users": 1000,
                                    "current_users": 25,
                                    "created_at": "2025-01-23T10:30:45.123456",
                                }
                            ],
                            "total": 100,
                            "next_cursor": "2025-01-23T10:30:45.123456",
                            "has_more": True,
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户管理"],
)
@inject
async def list_tenants(
    request: BaseRequest[ListTenantsRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    查询租户列表

    支持分页、搜索和筛选的租户列表查询
    """
    list_tenants_params = request.data
    result = await tenant_service.list_tenants(
        limit=list_tenants_params.limit,
        search=list_tenants_params.search,
        status=list_tenants_params.status,
    )
    return success_response(result, message="查询成功")


@router.post(
    "/detail",
    summary="获取租户详情",
    description="""
    根据租户ID获取租户详细信息

    **功能说明：**
    - 获取指定租户的完整详细信息
    - 包含租户基本信息、配置和统计数据
    - 支持缓存机制，提高查询性能
    - 实时计算用户、角色、权限等统计信息

    **返回数据：**
    - 租户基本信息：名称、编码、状态、描述等
    - 租户配置：最大用户数、自定义设置等
    - 统计信息：用户数量、角色数量、权限数量等
    - 时间信息：创建时间、更新时间等
    """,
    response_model=TenantDetailResponseModel,
    responses={
        200: {
            "description": "查询成功",
            "content": {
                "application/json": {
                    "examples": {
                        "status": "success",
                        "code": 200,
                        "message": "查询成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "tenant_name": "示例科技有限公司",
                            "tenant_code": "DEMO_TECH_CORP",
                            "status": "active",
                            "max_users": 1000,
                            "current_users": 25,
                            "statistics": {
                                "user_count": 25,
                                "active_user_count": 20,
                                "role_count": 5,
                                "permission_count": 15,
                            },
                            "created_at": "2025-01-23T10:30:45.123456",
                        },
                    }
                }
            },
        },
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户管理"],
)
@inject
async def get_tenant_detail(
    request: BaseRequest[GetTenantRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    获取租户详情

    根据租户ID获取完整的租户详细信息
    """
    get_tenant_params = request.data
    result = await tenant_service.get_tenant_detail(tenant_id=get_tenant_params.tenant_id)
    return success_response(result, message="查询成功")


@router.post(
    "/update",
    summary="更新租户信息",
    description="""
    更新指定租户的信息

    **功能说明：**
    - 支持部分字段更新，只传入需要修改的字段
    - 自动记录变更历史和审计日志
    - 更新后自动清理相关缓存
    - 支持租户名称、描述、配置等信息修改
    - 支持租户状态变更管理

    **业务规则：**
    - 租户编码不可修改
    - 租户名称更新时需要保证唯一性
    - 最大用户数不能小于当前用户数
    - 状态变更需要符合业务流程规则

    **返回数据：**
    - 更新后的租户完整信息
    - 本次更新的变更记录
    - 更新时间戳
    """,
    response_model=UpdateTenantResponseModel,
    responses={
        200: {
            "description": "更新成功",
            "content": {
                "application/json": {
                    "examples": {
                        "status": "success",
                        "code": 200,
                        "message": "租户信息更新成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "tenant_name": "更新后的企业名称",
                            "tenant_code": "DEMO_TECH_CORP",
                            "status": "active",
                            "max_users": 2000,
                            "updated_at": "2025-01-23T11:00:00.123456",
                            "changes": {
                                "tenant_name": {
                                    "old": "原企业名称",
                                    "new": "更新后的企业名称",
                                }
                            },
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "租户不存在"},
        409: {"description": "租户名称已存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户管理"],
)
@inject
async def update_tenant(
    request: BaseRequest[UpdateTenantRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    更新租户信息

    支持部分字段更新的租户信息修改
    """
    update_tenant_params = request.data
    result = await tenant_service.update_tenant(
        tenant_id=update_tenant_params.tenant_id,
        tenant_name=update_tenant_params.tenant_name,
        description=update_tenant_params.description,
        max_users=update_tenant_params.max_users,
        settings=update_tenant_params.settings,
        status=update_tenant_params.status,
    )
    return success_response(result, message="租户信息更新成功")


@router.post(
    "/delete",
    summary="删除租户",
    description="""
    删除指定租户

    **功能说明：**
    - 支持软删除和硬删除两种模式
    - 软删除：标记为已删除状态，保留数据便于恢复
    - 硬删除：物理删除数据，不可恢复
    - 删除前自动检查依赖关系
    - 支持强制删除模式，忽略依赖检查
    - 硬删除前可选择备份数据

    **业务规则：**
    - 默认为软删除模式，安全性更高
    - 存在活跃用户时不允许删除（除非强制删除）
    - 硬删除前会自动备份数据（可选）
    - 删除操作会记录详细的审计日志

    **返回数据：**
    - 删除操作结果和状态
    - 数据清理摘要
    - 备份信息（硬删除时）
    """,
    response_model=DeleteTenantResponseModel,
    responses={
        200: {
            "description": "删除成功",
            "content": {
                "application/json": {
                    "examples": {
                        "status": "success",
                        "code": 200,
                        "message": "租户删除成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "delete_type": "soft",
                            "status": "deleted",
                            "deleted_at": "2025-01-23T10:30:45.123456",
                            "cleanup_summary": {
                                "users_archived": 25,
                                "roles_removed": 5,
                                "permissions_removed": 15,
                            },
                        },
                    }
                }
            },
        },
        400: {"description": "请求参数错误"},
        404: {"description": "租户不存在"},
        409: {"description": "存在依赖关系，无法删除"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户管理"],
)
@inject
async def delete_tenant(
    request: BaseRequest[DeleteTenantRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    删除租户

    支持软删除和硬删除的租户删除操作
    """
    delete_tenant_params = request.data
    result = await tenant_service.delete_tenant(
        tenant_id=delete_tenant_params.tenant_id,
    )
    return success_response(result, message="租户删除成功")


# ================================
# 2.2 租户配置管理API端点
# ================================


@router.post(
    "/settings/get",
    summary="获取租户配置",
    description="""
    获取指定租户的配置信息

    **功能说明：**
    - 获取租户的完整配置信息
    - 自动合并默认配置和自定义配置
    - 返回配置的版本信息和更新时间
    - 支持配置缓存优化

    **返回配置项：**
    - 密码策略配置
    - 会话管理配置
    - 安全设置配置
    - 功能模块开关
    - 存储配额配置
    - 通知设置配置
    """,
    response_model=TenantSettingsResponseModel,
    responses={
        200: {
            "description": "获取成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "获取成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "settings": {
                                "password_policy": {
                                    "min_length": 8,
                                    "require_uppercase": True,
                                },
                                "session_config": {"session_timeout": 7200},
                            },
                            "last_updated": "2025-01-23T10:30:45.123456",
                            "settings_version": "1.0",
                            "is_default": False,
                        },
                    }
                }
            },
        },
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户配置管理"],
)
@inject
async def get_tenant_settings(
    request: BaseRequest[GetTenantSettingsRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    获取租户配置信息
    """
    get_settings_params = request.data
    result = await tenant_service.get_tenant_settings(tenant_id=get_settings_params.tenant_id)
    return success_response(result, message="获取成功")


@router.post(
    "/settings/update",
    summary="更新租户配置",
    description="""
    更新指定租户的配置信息

    **功能说明：**
    - 支持部分配置更新
    - 提供多种合并模式（merge、replace、patch）
    - 自动验证配置项的有效性
    - 记录配置变更审计日志
    - 自动清理相关缓存

    **合并模式说明：**
    - merge：合并模式，保留未指定的配置项
    - replace：替换模式，完全替换配置
    - patch：深度合并模式，递归合并嵌套对象

    **支持的配置项：**
    - password_policy：密码策略
    - session_config：会话配置
    - security_config：安全配置
    - feature_modules：功能模块开关
    - storage_config：存储配置
    - notification_config：通知配置
    """,
    response_model=UpdateTenantSettingsResponseModel,
    responses={
        200: {
            "description": "更新成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "配置更新成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "settings": {
                                "password_policy": {
                                    "min_length": 10,
                                    "require_special_chars": True,
                                }
                            },
                            "merge_mode": "merge",
                            "updated_keys": ["password_policy"],
                            "updated_at": "2025-01-23T10:30:45.123456",
                        },
                    }
                }
            },
        },
        400: {"description": "配置参数错误"},
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户配置管理"],
)
@inject
async def update_tenant_settings(
    request: BaseRequest[UpdateTenantSettingsRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    更新租户配置信息
    """
    update_settings_params = request.data
    result = await tenant_service.update_tenant_settings(
        tenant_id=update_settings_params.tenant_id,
        settings_update=update_settings_params.settings,
        merge_mode=update_settings_params.merge_mode,
        user_id=None,  # 实际应该从认证信息中获取
    )
    return success_response(result, message="配置更新成功")


@router.post(
    "/status/change",
    summary="变更租户状态",
    description="""
    变更指定租户的状态

    **功能说明：**
    - 支持租户状态的安全变更
    - 自动验证状态转换规则
    - 检查状态变更的前置条件
    - 处理状态变更的副作用
    - 记录状态变更审计日志

    **状态转换规则：**
    - pending → active, suspended, deleted
    - active → suspended, inactive, deleted
    - inactive → active, suspended, deleted
    - suspended → active, inactive, deleted
    - deleted → 无法转换到其他状态

    **变更原因：**
    - admin_action：管理员操作
    - quota_exceeded：配额超限
    - payment_overdue：付款逾期
    - security_violation：安全违规
    - maintenance：系统维护
    - user_request：用户请求
    """,
    response_model=ChangeTenantStatusResponseModel,
    responses={
        200: {
            "description": "状态变更成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "租户状态变更成功",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "old_status": "pending",
                            "new_status": "active",
                            "reason": "admin_action",
                            "comment": "管理员手动激活租户",
                            "changed_at": "2025-01-23T10:30:45.123456",
                            "changed_by": "admin_user_id",
                        },
                    }
                }
            },
        },
        400: {"description": "状态转换不合法"},
        404: {"description": "租户不存在"},
        409: {"description": "不满足状态变更条件"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户状态管理"],
)
@inject
async def change_tenant_status(
    request: BaseRequest[ChangeTenantStatusRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    变更租户状态
    """
    change_status_params = request.data
    result = await tenant_service.change_tenant_status(
        tenant_id=change_status_params.tenant_id,
        new_status=change_status_params.new_status,
        reason=change_status_params.reason,
        comment=change_status_params.comment,
        user_id=None,  # 实际应该从认证信息中获取
    )
    return success_response(result, message="租户状态变更成功")


@router.post(
    "/quotas/check",
    summary="检查租户配额",
    description="""
    检查指定租户的配额使用情况

    **功能说明：**
    - 检查用户数量配额
    - 检查存储空间配额
    - 检查API调用配额
    - 检查并发会话配额
    - 返回配额使用统计和警告信息

    **配额类型：**
    - users：用户数量配额
    - storage：存储空间配额
    - api_calls：API调用配额
    - sessions：并发会话配额

    **返回信息：**
    - 配额限制和当前使用量
    - 使用百分比和剩余配额
    - 是否超限和警告状态
    """,
    response_model=CheckTenantQuotasResponseModel,
    responses={
        200: {
            "description": "检查成功",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "配额检查完成",
                        "data": {
                            "tenant_id": "6e4bf872-3aa7-4e32-8d78-dd17b4b03c61",
                            "has_violations": False,
                            "violations": [],
                            "warnings": [],
                            "quotas": {
                                "users": {
                                    "limit": 1000,
                                    "current": 25,
                                    "usage_percentage": 2.5,
                                    "exceeded": False,
                                    "remaining": 975,
                                }
                            },
                            "checked_at": "2025-01-23T10:30:45.123456",
                        },
                    }
                }
            },
        },
        404: {"description": "租户不存在"},
        500: {"description": "服务器内部错误"},
    },
    tags=["租户配额管理"],
)
@inject
async def check_tenant_quotas(
    request: BaseRequest[CheckTenantQuotasRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    检查租户配额状态
    """
    check_quotas_params = request.data
    result = await tenant_service.check_tenant_quotas(tenant_id=check_quotas_params.tenant_id)
    return success_response(result, message="配额检查完成")


@router.post(
    "/status/auto_manage",
    summary="自动管理租户状态",
    description="""
    自动管理租户状态

    **功能说明：**
    - 基于配额使用情况自动调整租户状态
    - 支持单个租户或批量处理
    - 自动检测配额违规并执行相应操作
    - 记录自动管理的操作日志

    **自动管理规则：**
    - 配额超限时自动暂停租户
    - 配额恢复正常时自动激活租户
    - 根据业务规则执行状态转换

    **处理结果：**
    - 处理的租户数量
    - 执行的状态变更列表
    - 发现的配额违规情况
    - 处理过程中的错误信息
    """,
    response_model=AutoManageTenantStatusResponseModel,
    responses={
        200: {
            "description": "自动管理完成",
            "content": {
                "application/json": {
                    "example": {
                        "status": "success",
                        "code": 200,
                        "message": "自动状态管理完成",
                        "data": {
                            "processed_tenants": 5,
                            "status_changes": [
                                {
                                    "tenant_id": "tenant1",
                                    "old_status": "active",
                                    "new_status": "suspended",
                                    "reason": "quota_exceeded",
                                    "changed_at": "2025-01-23T10:30:45.123456",
                                }
                            ],
                            "quota_violations": [],
                            "errors": [],
                            "processed_at": "2025-01-23T10:30:45.123456",
                        },
                    }
                }
            },
        },
        500: {"description": "服务器内部错误"},
    },
    tags=["租户状态管理"],
)
@inject
async def auto_manage_tenant_status(
    request: BaseRequest[AutoManageTenantStatusRequest],
    tenant_service: TenantService = Depends(Provide[ServiceContainer.tenant_service]),
):
    """
    自动管理租户状态
    """
    auto_manage_params = request.data
    result = await tenant_service.auto_manage_tenant_status(tenant_id=auto_manage_params.tenant_id)
    return success_response(result, message="自动状态管理完成")
