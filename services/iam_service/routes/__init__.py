"""
IAM 服务路由模块

包含所有 API 路由的定义和注册
"""

from fastapi import APIRouter

from . import (
    advanced_security,
    audit,
    auth,
    documents,
    knowledge_bases,
    permissions,
    rbac,
    roles,
    system,
    system_config,
    tenants,
    users,
)

# 创建主路由器
api_router = APIRouter(prefix="/api/v1")

# 注册各模块路由
api_router.include_router(tenants.router, prefix="/tenants", tags=["租户管理"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(auth.router, prefix="/auth", tags=["认证与安全"])
api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
api_router.include_router(permissions.router, prefix="/permissions", tags=["权限管理"])
api_router.include_router(knowledge_bases.router, prefix="/knowledge_bases", tags=["知识库管理"])
api_router.include_router(documents.router, prefix="/documents", tags=["文档管理"])
api_router.include_router(system.router, prefix="/system", tags=["系统监控"])

# 第三阶段新增路由
api_router.include_router(rbac.router, tags=["RBAC权限管理"])
api_router.include_router(audit.router, tags=["审计日志"])
api_router.include_router(system_config.router, tags=["系统配置"])
api_router.include_router(advanced_security.router, tags=["高级安全"])

__all__ = ["api_router"]
