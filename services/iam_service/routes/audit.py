"""
审计日志路由

提供审计日志查询、分析和管理的API接口
"""

from typing import Any, Dict, List, Optional

from container import ServiceContainer
from dependency_injector.wiring import Provide, inject
from fastapi import APIRouter, Depends
from pydantic import BaseModel, Field

from commonlib.schemas.request import BaseRequest
from commonlib.schemas.responses import SuccessResponse, success_response
from services.audit_service import AuditService

router = APIRouter(prefix="/audit", tags=["审计日志"])


# ===== 请求数据模型 =====


class QueryAuditLogsRequestData(BaseModel):
    """
    查询审计日志请求数据模型

    支持多条件查询和时间范围过滤
    """

    tenant_id: str = Field(
        ..., description="租户ID", examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"],
    )
    action: Optional[str] = Field(None, description="操作类型", examples=["LOGIN"])
    resource_type: Optional[str] = Field(None, description="资源类型", examples=["USER"])
    resource_id: Optional[str] = Field(
        None,
        description="资源ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"],
    )
    start_time: Optional[str] = Field(
        None, description="开始时间（ISO格式）", examples=["2025-01-01T00:00:00.000000"]
    )
    end_time: Optional[str] = Field(
        None, description="结束时间（ISO格式）", examples=["2025-01-31T23:59:59.999999"]
    )
    ip_address: Optional[str] = Field(None, description="IP地址", examples=["*************"])
    status: Optional[str] = Field(
        None,
        description="操作状态：success(成功)、failed(失败)、warning(警告)",
        examples=["success"],
    )
    risk_level: Optional[str] = Field(
        None,
        description="风险级别：low(低)、medium(中)、high(高)、critical(严重)",
        examples=["medium"],
    )
    page: int = Field(1, description="页码", ge=1, examples=[1])
    page_size: int = Field(20, description="每页数量", ge=1, le=100, examples=[20])
    order_by: str = Field(
        "created_at",
        description="排序字段：created_at(创建时间)、risk_level(风险级别)",
        examples=["created_at"],
    )
    order_direction: str = Field(
        "desc", description="排序方向：asc(升序)、desc(降序)", examples=["desc"]
    )


class CreateAuditLogRequestData(BaseModel):
    """
    创建审计日志请求数据模型

    手动创建审计日志记录
    """

    tenant_id: str = Field(
        ..., description="租户ID", examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"],
    )
    action: str = Field(..., description="操作类型", examples=["CUSTOM_ACTION"])
    resource_type: str = Field(..., description="资源类型", examples=["DOCUMENT"])
    resource_id: Optional[str] = Field(
        None,
        description="资源ID",
        examples=["doc_550e8400-e29b-41d4-a716-446655440000"],
    )
    description: str = Field(..., description="操作描述", examples=["用户下载了敏感文档"])
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="详细信息",
        examples=[{"file_name": "confidential.pdf", "file_size": 1024000}],
    )
    ip_address: Optional[str] = Field(None, description="IP地址", examples=["*************"])
    user_agent: Optional[str] = Field(
        None,
        description="用户代理",
        examples=["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"],
    )
    status: str = Field("success", description="操作状态", examples=["success"])
    risk_level: str = Field("low", description="风险级别", examples=["medium"])


class GetAuditStatisticsRequestData(BaseModel):
    """
    获取审计统计请求数据模型

    获取审计日志的统计分析数据
    """

    tenant_id: str = Field(
        ..., description="租户ID", examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    start_time: Optional[str] = Field(
        None, description="开始时间（ISO格式）", examples=["2025-01-01T00:00:00.000000"]
    )
    end_time: Optional[str] = Field(
        None, description="结束时间（ISO格式）", examples=["2025-01-31T23:59:59.999999"]
    )
    group_by: str = Field(
        "day",
        description="分组方式：hour(小时)、day(天)、week(周)、month(月)",
        examples=["day"],
    )
    metrics: List[str] = Field(
        default_factory=lambda: ["total", "success", "failed"],
        description="统计指标",
        examples=[["total", "success", "failed", "high_risk"]],
    )


class ExportAuditLogsRequestData(BaseModel):
    """
    导出审计日志请求数据模型

    导出审计日志到文件
    """

    tenant_id: str = Field(
        ..., description="租户ID", examples=["550e8400-e29b-41d4-a716-446655440000"]
    )
    user_id: Optional[str] = Field(
        None,
        description="用户ID",
        examples=["user_550e8400-e29b-41d4-a716-446655440000"],
    )
    start_time: Optional[str] = Field(
        None, description="开始时间（ISO格式）", examples=["2025-01-01T00:00:00.000000"]
    )
    end_time: Optional[str] = Field(
        None, description="结束时间（ISO格式）", examples=["2025-01-31T23:59:59.999999"]
    )
    format: str = Field("csv", description="导出格式：csv、excel、json", examples=["csv"])
    include_details: bool = Field(True, description="是否包含详细信息", examples=[True])


# ===== 响应数据模型 =====


class AuditLogInfo(BaseModel):
    """审计日志信息模型"""

    log_id: str = Field(
        ...,
        description="日志ID",
        examples=["audit_550e8400-e29b-41d4-a716-446655440000"],
    )
    tenant_id: str = Field(..., description="租户ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    username: Optional[str] = Field(None, description="用户名")
    action: str = Field(..., description="操作类型", examples=["LOGIN"])
    resource_type: str = Field(..., description="资源类型", examples=["USER"])
    resource_id: Optional[str] = Field(None, description="资源ID")
    description: str = Field(..., description="操作描述", examples=["用户登录系统"])
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    status: str = Field(..., description="操作状态", examples=["success"])
    risk_level: str = Field(..., description="风险级别", examples=["low"])
    created_at: str = Field(..., description="创建时间")


class QueryAuditLogsResponse(BaseModel):
    """查询审计日志响应数据"""

    logs: List[AuditLogInfo] = Field(..., description="审计日志列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    has_next: bool = Field(..., description="是否有下一页")


class CreateAuditLogResponse(BaseModel):
    """创建审计日志响应数据"""

    log_id: str = Field(..., description="日志ID")
    created_at: str = Field(..., description="创建时间")


class AuditStatisticsResponse(BaseModel):
    """审计统计响应数据"""

    period: str = Field(..., description="统计周期")
    total_logs: int = Field(..., description="总日志数")
    success_logs: int = Field(..., description="成功日志数")
    failed_logs: int = Field(..., description="失败日志数")
    high_risk_logs: int = Field(..., description="高风险日志数")
    top_actions: List[Dict[str, Any]] = Field(..., description="热门操作")
    top_users: List[Dict[str, Any]] = Field(..., description="活跃用户")
    risk_distribution: Dict[str, int] = Field(..., description="风险分布")
    time_series: List[Dict[str, Any]] = Field(..., description="时间序列数据")


class ExportAuditLogsResponse(BaseModel):
    """导出审计日志响应数据"""

    export_id: str = Field(..., description="导出任务ID")
    download_url: str = Field(..., description="下载链接")
    file_name: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    record_count: int = Field(..., description="记录数量")
    created_at: str = Field(..., description="创建时间")
    expires_at: str = Field(..., description="过期时间")


# ===== 响应模型包装类 =====


class QueryAuditLogsResponseModel(SuccessResponse[QueryAuditLogsResponse]):
    """查询审计日志响应模型"""

    data: QueryAuditLogsResponse = Field(..., description="审计日志查询结果")


class CreateAuditLogResponseModel(SuccessResponse[CreateAuditLogResponse]):
    """创建审计日志响应模型"""

    data: CreateAuditLogResponse = Field(..., description="创建审计日志结果")


class AuditStatisticsResponseModel(SuccessResponse[AuditStatisticsResponse]):
    """审计统计响应模型"""

    data: AuditStatisticsResponse = Field(..., description="审计统计数据")


class ExportAuditLogsResponseModel(SuccessResponse[ExportAuditLogsResponse]):
    """导出审计日志响应模型"""

    data: ExportAuditLogsResponse = Field(..., description="导出任务信息")


# ===== 路由端点 =====


@router.post(
    "/logs/query",
    summary="查询审计日志",
    description="""
    查询审计日志

    **功能说明：**
    - 支持多条件查询和时间范围过滤
    - 支持分页和排序
    - 支持风险级别过滤
    - 支持操作状态过滤

    **查询条件：**
    - 用户ID、操作类型、资源类型过滤
    - 时间范围过滤
    - IP地址过滤
    - 状态和风险级别过滤

    **返回数据：**
    - 审计日志列表
    - 分页信息
    - 用户和操作详情
    """,
    response_model=QueryAuditLogsResponseModel,
    responses={
        200: {"description": "查询成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["日志查询"],
)
@inject
async def query_audit_logs(
    request: BaseRequest[QueryAuditLogsRequestData],
    audit_service: AuditService = Depends(Provide[ServiceContainer.audit_service]),
):
    """
    查询审计日志

    支持多条件查询和分页的审计日志查询
    """
    query_params = request.data
    result = await audit_service.query_audit_logs(
        tenant_id=query_params.tenant_id,
        user_id=query_params.user_id,
        action=query_params.action,
        resource_type=query_params.resource_type,
        resource_id=query_params.resource_id,
        start_time=query_params.start_time,
        end_time=query_params.end_time,
        ip_address=query_params.ip_address,
        status=query_params.status,
        risk_level=query_params.risk_level,
        page=query_params.page,
        page_size=query_params.page_size,
        order_by=query_params.order_by,
        order_direction=query_params.order_direction,
    )
    return success_response(result, message="查询成功")


@router.post(
    "/logs/create",
    summary="创建审计日志",
    description="""
    创建审计日志

    **功能说明：**
    - 手动创建审计日志记录
    - 支持自定义操作类型和风险级别
    - 自动记录时间戳和请求信息
    - 支持详细信息记录

    **使用场景：**
    - 外部系统集成审计
    - 自定义业务操作记录
    - 安全事件手动记录

    **返回数据：**
    - 日志ID
    - 创建时间
    """,
    response_model=CreateAuditLogResponseModel,
    responses={
        200: {"description": "创建成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["日志管理"],
)
@inject
async def create_audit_log(
    request: BaseRequest[CreateAuditLogRequestData],
    audit_service: AuditService = Depends(Provide[ServiceContainer.audit_service]),
):
    """
    创建审计日志

    手动创建审计日志记录
    """
    log_params = request.data
    result = await audit_service.create_audit_log(
        tenant_id=log_params.tenant_id,
        user_id=log_params.user_id,
        action=log_params.action,
        resource_type=log_params.resource_type,
        resource_id=log_params.resource_id,
        description=log_params.description,
        details=log_params.details,
        ip_address=log_params.ip_address,
        user_agent=log_params.user_agent,
        status=log_params.status,
        risk_level=log_params.risk_level,
    )
    return success_response(result, message="审计日志创建成功")


@router.post(
    "/statistics",
    summary="获取审计统计",
    description="""
    获取审计统计

    **功能说明：**
    - 获取审计日志的统计分析数据
    - 支持时间范围和分组统计
    - 提供多种统计指标
    - 支持趋势分析

    **统计指标：**
    - 总日志数、成功/失败数量
    - 风险级别分布
    - 热门操作和活跃用户
    - 时间序列趋势

    **返回数据：**
    - 统计概览
    - 分布数据
    - 趋势图表数据
    """,
    response_model=AuditStatisticsResponseModel,
    responses={
        200: {"description": "获取成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["统计分析"],
)
@inject
async def get_audit_statistics(
    request: BaseRequest[GetAuditStatisticsRequestData],
    audit_service: AuditService = Depends(Provide[ServiceContainer.audit_service]),
):
    """
    获取审计统计

    获取审计日志的统计分析数据
    """
    stats_params = request.data
    result = await audit_service.get_audit_statistics(
        tenant_id=stats_params.tenant_id,
        start_time=stats_params.start_time,
        end_time=stats_params.end_time,
        group_by=stats_params.group_by,
        metrics=stats_params.metrics,
    )
    return success_response(result, message="统计数据获取成功")


@router.post(
    "/logs/export",
    summary="导出审计日志",
    description="""
    导出审计日志

    **功能说明：**
    - 导出审计日志到文件
    - 支持多种导出格式
    - 支持条件过滤导出
    - 异步导出处理

    **导出格式：**
    - CSV：适合数据分析
    - Excel：适合报表查看
    - JSON：适合程序处理

    **返回数据：**
    - 导出任务ID
    - 下载链接
    - 文件信息
    """,
    response_model=ExportAuditLogsResponseModel,
    responses={
        200: {"description": "导出任务创建成功"},
        400: {"description": "请求参数错误"},
        500: {"description": "服务器内部错误"},
    },
    tags=["数据导出"],
)
@inject
async def export_audit_logs(
    request: BaseRequest[ExportAuditLogsRequestData],
    audit_service: AuditService = Depends(Provide[ServiceContainer.audit_service]),
):
    """
    导出审计日志

    创建审计日志导出任务
    """
    export_params = request.data
    result = await audit_service.export_audit_logs(
        tenant_id=export_params.tenant_id,
        user_id=export_params.user_id,
        start_time=export_params.start_time,
        end_time=export_params.end_time,
        format=export_params.format,
        include_details=export_params.include_details,
    )
    return success_response(result, message="导出任务创建成功")
