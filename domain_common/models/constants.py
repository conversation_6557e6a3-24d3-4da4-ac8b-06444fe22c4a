"""
数据库常量和枚举模块。

此模块包含领域模型中使用的所有数据库相关常量、
命名约定和枚举值。
"""

from typing import Any, Dict

PLATFORM_TENANT_ID = "00000000-0000-0000-0000-000000000000"
# ================================
# 类型别名
# ================================

# JSON类型别名 - 使用通用JSON而不是PostgreSQL特定的JSONB
JSONType = Dict[str, Any]


# ================================
# 状态枚举
# ================================
class SimpleState:
    """通用状态流转抽象基类"""

    # 子类需定义：状态常量和状态集合
    ALL: set[str] = set()
    ALLOWED_TRANSITIONS: dict[str, set[str]] = {}

    @classmethod
    def can_transition(cls, from_status: str, to_status: str) -> bool:
        """判断是否允许从一个状态流转到另一个状态"""
        return to_status in cls.ALLOWED_TRANSITIONS.get(from_status, set())

    @classmethod
    def is_valid(cls, status: str) -> bool:
        """判断是否为合法状态"""
        return status in cls.ALL

    @classmethod
    def all(cls) -> list[str]:
        """返回所有状态（用于 schema Enum 校验等）"""
        return list(cls.ALL)


class CommonStatus:
    """通用状态枚举。

    此枚举定义了可在不同领域模型中使用的标准状态值，
    以保持一致性。

    属性:
        ACTIVE: 实体激活且可操作
        INACTIVE: 实体非激活但未删除
        PENDING: 实体待审批或处理
        SUSPENDED: 实体临时暂停
        DELETED: 实体标记为已删除
        LOCKED: 实体被锁定无法修改
    """

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"
    DELETED = "deleted"
    LOCKED = "locked"
    all_status = [ACTIVE, INACTIVE, PENDING, SUSPENDED, DELETED, LOCKED]


class PermissionStatus(SimpleState):
    """权限状态常量（简单设计）

    - ACTIVE: 已启用，可分配、可操作
    - INACTIVE: 已停用，不可新分配，但已有绑定保留
    - PENDING: 待审批/待生效
    - LOCKED: 冻结，禁止任何修改
    - DELETED: 已删除（逻辑删除，仅审计保留）
    """

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    LOCKED = "locked"
    DELETED = "deleted"

    # 所有状态集合
    ALL = {ACTIVE, INACTIVE, PENDING, LOCKED, DELETED}

    # 允许的状态流转（简化版）
    ALLOWED_TRANSITIONS = {
        PENDING: {ACTIVE, DELETED},  # 审批通过/拒绝
        ACTIVE: {INACTIVE, LOCKED, DELETED},
        INACTIVE: {ACTIVE, DELETED},
        LOCKED: {ACTIVE},  # 只能解锁回 ACTIVE
        DELETED: set(),  # 不可恢复
    }


class UserStatus(SimpleState):
    ENABLED = "enabled"
    DISABLED = "disabled"
    LOCKED = "locked"
    DELETED = "deleted"

    ALL = {ENABLED, DISABLED, LOCKED, DELETED}

    ALLOWED_TRANSITIONS = {
        ENABLED: {DISABLED, LOCKED},
        DISABLED: {ENABLED},
        LOCKED: {ENABLED},
        DELETED: set(),
    }


class SecurityStatus:
    """安全事件状态枚举

    用于表示安全事件（如漏洞、告警等）的处理状态，
    适用于安全运维领域的模型定义。

    属性:
        PENDING: 待处理状态，等待安全团队初步分析
        INVESTIGATING: 调查中，安全团队正在处理该事件
        RESOLVED: 已解决，安全问题已被修复或确认消除
        FALSE_POSITIVE: 误报，经分析确认不是真实的安全威胁
    """

    PENDING = "pending"
    INVESTIGATING = "investigating"
    RESOLVED = "resolved"
    FALSE_POSITIVE = "false_positive"


# ================================
# 字段长度常量
# ================================


class FieldLengths:
    """标准字段长度常量，用于保持数据库架构一致性。"""

    # ID字段
    ID_LENGTH = 64

    # 字符串字段
    SHORT_STRING = 100
    MEDIUM_STRING = 255
    LONG_STRING = 500

    # 编码/名称字段
    CODE_LENGTH = 100
    NAME_LENGTH = 255

    # 状态字段
    STATUS_LENGTH = 20

    # 邮箱字段
    EMAIL_LENGTH = 320  # RFC 5321 标准

    # 电话字段
    PHONE_LENGTH = 20


# ================================
# 默认值
# ================================


class DefaultValues:
    """常用字段的默认值。"""

    # JSON字段默认值
    EMPTY_JSON = {}

    # 状态默认值
    DEFAULT_STATUS = CommonStatus.PENDING
    ACTIVE_STATUS = CommonStatus.ACTIVE

    # 数值默认值
    DEFAULT_RETRY_COUNT = 0
    DEFAULT_FAILED_ATTEMPTS = 0


# ================================
# 角色和用户组相关常量
# ================================


class RoleType:
    """角色类型枚举

    定义系统中不同类型的角色，用于区分平台级和租户级角色
    """

    # 平台级角色
    PLATFORM_SUPER_ADMIN = "platform_super_admin"  # 平台超级管理员
    PLATFORM_ADMIN = "platform_admin"  # 平台管理员

    # 租户级角色
    TENANT_SUPER_ADMIN = "tenant_super_admin"  # 租户超级管理员
    TENANT_ADMIN = "tenant_admin"  # 租户管理员
    TENANT_USER = "tenant_user"  # 租户普通用户

    # 自定义角色
    CUSTOM = "custom"  # 自定义角色

    # 所有角色类型
    ALL_TYPES = [
        PLATFORM_SUPER_ADMIN,
        PLATFORM_ADMIN,
        TENANT_SUPER_ADMIN,
        TENANT_ADMIN,
        TENANT_USER,
        CUSTOM,
    ]

    # 平台级角色集合
    PLATFORM_ROLES = {PLATFORM_SUPER_ADMIN, PLATFORM_ADMIN}

    # 租户级角色集合
    TENANT_ROLES = {TENANT_SUPER_ADMIN, TENANT_ADMIN, TENANT_USER, CUSTOM}


class UserGroupType:
    """用户组类型枚举

    定义不同类型的用户组，用于组织和权限管理
    """

    # 组织结构类型
    DEPARTMENT = "department"  # 部门
    TEAM = "team"  # 小组/团队
    PROJECT = "project"  # 项目组

    # 功能类型
    ROLE_GROUP = "role_group"  # 角色组（权限复用）
    PERMISSION_GROUP = "permission_group"  # 权限组

    # 临时类型
    TEMPORARY = "temporary"  # 临时组

    # 所有用户组类型
    ALL_TYPES = [DEPARTMENT, TEAM, PROJECT, ROLE_GROUP, PERMISSION_GROUP, TEMPORARY]


class AssignmentType:
    """分配类型枚举

    定义用户、角色、权限等的分配类型
    """

    PERMANENT = "permanent"  # 永久分配
    TEMPORARY = "temporary"  # 临时分配
    INHERITED = "inherited"  # 继承分配

    ALL_TYPES = [PERMANENT, TEMPORARY, INHERITED]


# ================================
# 数据库架构常量
# ================================


class SchemaConstants:
    """数据库架构相关常量。"""

    # PostgreSQL的UUID生成函数
    UUID_FUNCTION = "uuid_generate_v4()::varchar"

    # 时区感知的时间戳函数
    CURRENT_TIMESTAMP = "current_timestamp()"

    # 常用索引名称
    TENANT_INDEX_SUFFIX = "_tenant_idx"
    STATUS_INDEX_SUFFIX = "_status_idx"
    CREATED_AT_INDEX_SUFFIX = "_created_at_idx"
