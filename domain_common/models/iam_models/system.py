"""
IAM 系统模型

包含系统配置、缓存键等系统级实体
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Boolean, Index, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from domain_common.models.base_model import Base
from domain_common.models.constants import JSONType
from domain_common.models.fields import Fields


class SystemConfig(Base):
    """系统配置模型

    存储系统级别的配置信息，支持动态配置管理
    """

    __tablename__ = "system_configs"

    # 重写主键为 config_id
    config_id: Mapped[str] = Fields.uuid_primary_key(doc="配置ID")

    # 配置基本信息字段
    config_key: Mapped[str] = Fields.name(doc="配置键")
    config_value: Mapped[Optional[JSONType]] = Fields.json_field(doc="配置值，JSON格式")
    config_type: Mapped[str] = Fields.code(
        max_length=50, doc="配置类型: string, number, boolean, object, array"
    )
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="配置描述")

    # 配置管理字段
    is_encrypted: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否加密存储")
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否公开可读")
    version: Mapped[str] = Fields.code(max_length=20, doc="配置版本")
    category: Mapped[str] = mapped_column(
        String(50), nullable=False, default="general", doc="配置分类"
    )

    # 审计字段
    created_by: Mapped[Optional[str]] = Fields.created_by()
    updated_by: Mapped[Optional[str]] = Fields.updated_by()

    __table_args__ = (
        UniqueConstraint("config_key", name="uq_system_configs_config_key"),
        Index("idx_system_configs_key", "config_key"),
        Index("idx_system_configs_category", "category"),
        Index("idx_system_configs_type", "config_type"),
        Index("idx_system_configs_public", "is_public"),
        Index("idx_system_configs_created_at", "created_at"),
    )

    def __repr__(self) -> str:
        return (
            f"<SystemConfig(config_id={self.config_id}, "
            f"config_key={self.config_key}, config_type={self.config_type})>"
        )


class TenantConfig(Base):
    """租户配置模型

    存储租户级别的配置信息，支持租户级配置管理
    """

    __tablename__ = "tenant_configs"

    # 重写主键为 config_id
    config_id: Mapped[str] = Fields.uuid_primary_key(doc="配置ID")

    # 租户关联
    tenant_id: Mapped[str] = Fields.tenant()

    # 配置基本信息字段
    config_key: Mapped[str] = Fields.name(doc="配置键")
    config_value: Mapped[Optional[JSONType]] = Fields.json_field(doc="配置值，JSON格式")
    config_type: Mapped[str] = Fields.code(
        max_length=50, doc="配置类型: string, number, boolean, object, array"
    )
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="配置描述")

    # 配置管理字段
    is_encrypted: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否加密存储")
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, doc="是否公开可读")
    version: Mapped[str] = Fields.code(max_length=20, doc="配置版本")
    category: Mapped[str] = mapped_column(
        String(50), nullable=False, default="general", doc="配置分类"
    )
    # 审计字段
    created_by: Mapped[Optional[str]] = Fields.created_by()
    updated_by: Mapped[Optional[str]] = Fields.updated_by()

    __table_args__ = (
        UniqueConstraint("tenant_id", "config_key", name="uq_tenant_configs_tenant_key"),
        Index("idx_tenant_configs_tenant", "tenant_id"),
        Index("idx_tenant_configs_key", "config_key"),
        Index("idx_tenant_configs_category", "category"),
        Index("idx_tenant_configs_type", "config_type"),
        Index("idx_tenant_configs_public", "is_public"),
        Index("idx_tenant_configs_created_at", "created_at"),
    )

    def __repr__(self) -> str:
        return (
            f"<TenantConfig(config_id={self.config_id}, "
            f"tenant_id={self.tenant_id}, config_key={self.config_key})>"
        )


class CacheKey(Base):
    """缓存键模型

    管理系统中使用的缓存键，用于缓存管理和清理
    """

    __tablename__ = "cache_keys"

    # 使用BIGSERIAL作为主键（匹配数据库schema中的BIGSERIAL）
    id: Mapped[int] = Fields.bigint_id(doc="缓存键记录ID")

    # 缓存键基本信息字段
    cache_key: Mapped[str] = Fields.name(doc="缓存键")
    cache_type: Mapped[str] = Fields.code(max_length=50, doc="缓存类型")
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="缓存描述")

    # 缓存管理字段
    ttl_seconds: Mapped[Optional[int]] = mapped_column(nullable=True, doc="TTL时间（秒）")
    last_accessed: Mapped[Optional[datetime]] = mapped_column(nullable=True, doc="最后访问时间")
    access_count: Mapped[int] = mapped_column(default=0, doc="访问次数")

    __table_args__ = (
        UniqueConstraint("cache_key", name="uq_cache_keys_cache_key"),
        Index("idx_cache_keys_type", "cache_type"),
        Index("idx_cache_keys_last_accessed", "last_accessed"),
        Index("idx_cache_keys_created_at", "created_at"),
    )

    def __repr__(self) -> str:
        return f"<CacheKey(id={self.id}, cache_key={self.cache_key}, cache_type={self.cache_type})>"


class SecurityPolicy(Base):
    """安全策略模型

    存储系统安全策略配置，支持租户级和全局策略
    """

    __tablename__ = "security_policies"

    # 重写主键为 policy_id
    policy_id: Mapped[str] = Fields.uuid_primary_key(doc="策略ID")

    # 租户关联（可选，为空表示全局策略）
    tenant_id: Mapped[Optional[str]] = Fields.tenant(required=False)

    # 策略基本信息
    policy_name: Mapped[str] = Fields.name(doc="策略名称")
    policy_config: Mapped[JSONType] = Fields.json_field(doc="策略配置，JSON格式")
    enabled: Mapped[bool] = mapped_column(Boolean, default=True, doc="是否启用")

    # 策略元信息
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="策略描述")
    version: Mapped[str] = mapped_column(String(20), default="1.0", doc="策略版本")
    # 审计字段
    created_by: Mapped[Optional[str]] = Fields.created_by()
    updated_by: Mapped[Optional[str]] = Fields.updated_by()

    __table_args__ = (
        UniqueConstraint("tenant_id", "policy_name", name="uq_security_policies_tenant_policy"),
        Index("idx_security_policies_tenant", "tenant_id"),
        Index("idx_security_policies_enabled", "enabled"),
        Index("idx_security_policies_created_at", "created_at"),
    )

    def __repr__(self) -> str:
        return (
            f"<SecurityPolicy(policy_id={self.policy_id}, "
            f"policy_name={self.policy_name}, tenant_id={self.tenant_id})>"
        )


class SecurityEvent(Base):
    """安全事件模型

    记录系统安全事件和威胁检测结果
    """

    __tablename__ = "security_events"

    # 重写主键为 event_id
    event_id: Mapped[str] = Fields.uuid_primary_key(doc="事件ID")

    # 租户和用户关联
    tenant_id: Mapped[str] = Fields.tenant()
    user_id: Mapped[Optional[str]] = Fields.user()

    # 事件基本信息
    event_type: Mapped[str] = Fields.code(max_length=50, doc="事件类型")
    severity: Mapped[str] = Fields.code(max_length=20, doc="严重程度: low, medium, high, critical")
    title: Mapped[str] = Fields.name(doc="事件标题")
    description: Mapped[str] = mapped_column(Text, doc="事件描述")

    # 事件详情
    details: Mapped[JSONType] = Fields.json_field(doc="事件详细信息，JSON格式")

    # 请求信息
    ip_address: Mapped[Optional[str]] = Fields.ip_address()
    user_agent: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="用户代理")

    # 处理状态
    status: Mapped[str] = Fields.security_status()
    assigned_to: Mapped[Optional[str]] = Fields.user()
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True, doc="处理备注")

    # 审计字段
    created_by: Mapped[Optional[str]] = Fields.created_by()
    updated_by: Mapped[Optional[str]] = Fields.updated_by()

    __table_args__ = (
        Index("idx_security_events_tenant", "tenant_id"),
        Index("idx_security_events_user", "user_id"),
        Index("idx_security_events_type", "event_type"),
        Index("idx_security_events_severity", "severity"),
        Index("idx_security_events_status", "status"),
        Index("idx_security_events_created_at", "created_at"),
        Index("idx_security_events_ip", "ip_address"),
    )

    def __repr__(self) -> str:
        return (
            f"<SecurityEvent(event_id={self.event_id}, "
            f"event_type={self.event_type}, severity={self.severity}, tenant_id={self.tenant_id})>"
        )
