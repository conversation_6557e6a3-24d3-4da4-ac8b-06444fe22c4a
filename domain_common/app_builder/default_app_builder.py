from typing import Dict

from fastapi import APIRouter, FastAPI

from commonlib.configs.base_setting import AppSettings
from commonlib.core.tsif_logging import app_logger
from commonlib.exceptions.exception_handlers import app_exception_handlers
from commonlib.middlewares import (
    setup_cors_middleware,
    setup_metrics_middleware,
    setup_security_middleware,
)
from commonlib.middlewares.logging_middleware import setup_logging_middleware
from commonlib.middlewares.request_id_middleware import setup_request_id_middleware
from commonlib.schemas.responses import ErrorResponse, FailResponse
from commonlib.utils.scheduler_tasks_context import scheduler_tasks_mgm
from domain_common.common_health import health


class AppBuilder:
    @staticmethod
    def get_common_responses() -> Dict[int, Dict[str, any]]:
        """获取标准公共响应模型"""
        return {
            400: {"description": "错误请求", "model": FailResponse},
            422: {"description": "参数校验失败", "model": FailResponse},
            500: {"description": "系统错误", "model": ErrorResponse},
        }

    @staticmethod
    def start_app_scheduler():
        scheduler_tasks_mgm.start()

    @staticmethod
    def close_app_scheduler():
        scheduler_tasks_mgm.stop()

    @staticmethod
    def register_routers(
        application: FastAPI,
        routers: Dict[str, APIRouter],
        prefix: str = "",
        common_responses: bool = True,
    ) -> None:
        """
        标准化路由注册
        :param application:
        :param routers: {路由路径: router实例}
        :param prefix: 统一前缀
        :param common_responses: 是否添加公共响应模型
        """
        responses = AppBuilder.get_common_responses() if common_responses else {}

        for path, router in routers.items():
            full_path = f"{prefix.rstrip('/')}/{path.lstrip('/')}"
            application.include_router(router, prefix=full_path, responses=responses)

    @staticmethod
    def register_health_router(application: FastAPI, prefix: str = "/api/v1") -> FastAPI:
        """安全注册健康检查路由"""

        AppBuilder.register_routers(
            application=application,
            routers={
                "health": health.router,
            },
            prefix=prefix,
        )
        return application

    @staticmethod
    def register_exception_handlers(application: FastAPI, config: AppSettings) -> None:
        """Register all exception handlers in a single operation."""

        for exception_type, handler in app_exception_handlers.items():
            application.add_exception_handler(exception_type, handler)
        app_logger.info(
            f"Registered {len(app_exception_handlers)} exception handlers",
            handlers=list(app_exception_handlers.keys()),
        )

    @staticmethod
    def setup_middlewares(application: FastAPI, config: AppSettings) -> None:
        """Configure middlewares in proper order with type safety."""
        middleware_setup_functions = [
            # 跨域最先处理，方便OPTIONS请求快速响应
            lambda app: setup_cors_middleware(app, config),
            # 其次安全校验，拦截非法请求
            lambda app: setup_security_middleware(app, config),
            # 再生成 request_id，供后续使用
            setup_request_id_middleware,
            # 记录日志，依赖request_id
            setup_logging_middleware,
            # 指标监控放最后，不影响业务
            lambda app: setup_metrics_middleware(app, log_interval=60),
        ]

        for setup_func in middleware_setup_functions[::-1]:
            setup_func(application)
