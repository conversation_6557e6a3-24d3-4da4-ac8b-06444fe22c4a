from typing import Any, Dict, List, Optional

from motor.motor_asyncio import AsyncIOMotorCollection, AsyncIOMotorDatabase
from pymongo import IndexModel, ReturnDocument
from pymongo.results import DeleteResult

from commonlib.storages.persistence.mongodb.client import MongoConnector


class MongoRepository:
    """
    增强版MongoDB仓储实现：
    1. 完全类型注解支持
    2. 异步生成器支持批量查询
    3. 统一的软删除过滤与更新字段处理
    4. 连接池及会话管理
    """

    def __init__(self, client: MongoConnector, db_name: str, collection_name: str):
        self._client = client
        self._db_name = db_name
        self._collection_name = collection_name

    @property
    def database(self) -> AsyncIOMotorDatabase:
        return self._client.get_client().get_database(self._db_name)

    @property
    def collection(self) -> AsyncIOMotorCollection:
        return self.database.get_collection(self._collection_name)

    async def find_one(
        self,
        query_filter: Dict[str, Any],
        *,
        projection: Optional[Dict[str, Any]] = None,
        sort: Optional[List[tuple]] = None,
        **kwargs,
    ) -> Optional[Dict]:
        doc = await self.collection.find_one(
            query_filter, projection=projection, sort=sort, **kwargs
        )
        return doc

    async def find_many(
        self,
        query_filter: Dict[str, Any],
        *,
        skip: Optional[int] = None,
        limit: Optional[int] = None,
        sort: Optional[List[tuple]] = None,
        projection: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> List[Dict]:
        filter_condition = {**kwargs}
        if skip:
            filter_condition["skip"] = skip
        if limit:
            filter_condition["limit"] = limit
        if sort:
            filter_condition["sort"] = sort
        cursor = self.collection.find(
            query_filter,
            projection=projection,
            **filter_condition,
        )

        return [doc async for doc in cursor]

    async def insert_one(self, doc, *, session: Optional[Any] = None) -> str:
        result = await self.collection.insert_one(doc, session=session)
        return str(result.inserted_id)

    async def insert_many(
        self, docs: List[Dict], *, ordered: bool = False, session: Optional[Any] = None
    ) -> List[str]:
        result = await self.collection.insert_many(docs, ordered=ordered, session=session)
        return [str(_id) for _id in result.inserted_ids]

    async def update_one(
        self,
        query_filter: Dict[str, Any],
        update_fields: Dict[str, Any],
        *,
        upsert=False,
        session: Optional[Any] = None,
    ) -> int:
        res = await self.collection.update_one(
            query_filter, update_fields, upsert=upsert, session=session
        )
        return res.modified_count

    async def update_many(
        self,
        query_filter: Dict[str, Any],
        update_fields: Dict[str, Any],
        *,
        session: Optional[Any] = None,
    ) -> int:
        res = await self.collection.update_many(query_filter, update_fields, session=session)
        return res.modified_count

    async def find_one_and_upsert(
        self,
        query_filter: Dict[str, Any],
        update_fields: Dict[str, Any],
        *,
        return_document: ReturnDocument = ReturnDocument.AFTER,
        session: Optional[Any] = None,
    ) -> Optional[Dict]:
        """
        查找并 upsert 单条文档：
        :param query_filter: 查询条件
        :param update_fields: 待更新字段
        :param return_document: 返回 BEFORE 或 AFTER
        :param session: 事务 Session
        :return: 实体对象或 None
        """

        doc = await self.collection.find_one_and_update(
            query_filter,
            update_fields,
            upsert=True,
            session=session,
            return_document=bool(return_document),
        )
        return doc

    async def delete_one(
        self,
        query_filter: Dict[str, Any],
        *,
        session: Optional[Any] = None,
    ) -> DeleteResult:
        return await self.collection.delete_one(query_filter, session=session)

    async def delete_many(
        self, query_filter: Dict[str, Any], *, session: Optional[Any] = None
    ) -> DeleteResult:
        return await self.collection.delete_many(query_filter, session=session)

    async def distinct(
        self,
        key: str,
        query_filter: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> List[Any]:
        return await self.collection.distinct(key, filter=query_filter, **kwargs)

    async def count_documents(self, query_filter: Dict[str, Any], **kwargs) -> int:
        return await self.collection.count_documents(query_filter, **kwargs)

    async def bulk_write(
        self,
        operations: List[Any],
        *,
        ordered: bool = True,
        session: Optional[Any] = None,
    ) -> int:
        res = await self.collection.bulk_write(operations, ordered=ordered, session=session)
        return res.modified_count

    async def aggregate(
        self,
        pipeline: List[Dict[str, Any]],
        *,
        allow_disk_use: bool = False,
        session: Optional[Any] = None,
        **kwargs,
    ) -> List[Dict[str, Any]]:

        cursor = self.collection.aggregate(
            pipeline, allowDiskUse=allow_disk_use, session=session, **kwargs
        )
        return [doc async for doc in cursor]

    async def create_indexes(self, indexes: List[Dict[str, Any]], **kwargs):
        index_models = [IndexModel(idx["keys"], **idx.get("options", {})) for idx in indexes]
        await self.collection.create_indexes(index_models)

    async def exists(self, query_filter: Dict[str, Any], **kwargs) -> bool:
        doc = await self.collection.find_one(query_filter, projection={"_id": 1}, **kwargs)
        return doc is not None

    async def with_transaction(self, callback: callable, **kwargs) -> Any:
        """增强事务处理"""
        async with await self.collection.database.client.start_session() as session:
            try:
                async with session.start_transaction():
                    result = await callback(session)
                    await session.commit_transaction()
                    return result
            except Exception as e:
                await session.abort_transaction()
                raise e
