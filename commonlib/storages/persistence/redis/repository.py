from typing import Any, Dict, List, Optional, Set, TypeVar

from redis.asyncio import Redis

from commonlib.storages.persistence.redis.client import RedisConnector

T = TypeVar("T")


class RedisRepository:
    def __init__(
        self,
        redis_connector: RedisConnector,
        key_prefix: str = "",
    ):
        self._redis_connector = redis_connector
        self._key_prefix = key_prefix

    @property
    def client(self) -> Redis:
        return self._redis_connector.get_client()

    def make_key(self, key: str) -> str:
        return f"{self._key_prefix}:{key}" if self._key_prefix else key

    async def set(
        self,
        key: str,
        value: T,
        ttl: Optional[int] = None,
        nx: Optional[bool] = False,
        **kwargs,
    ) -> bool:
        full_key = self.make_key(key)
        return await self.client.set(full_key, value, ex=ttl, nx=nx)

    async def get(self, key: str) -> Optional[T]:
        full_key = self.make_key(key)
        data = await self.client.get(full_key)
        return data or None

    async def delete(self, key: str) -> bool:
        return bool(await self.client.delete(self.make_key(key)))

    async def exists(self, key: str) -> bool:
        return bool(await self.client.exists(self.make_key(key)))

    async def keys(self, pattern: str) -> List[str]:
        """获取匹配模式的所有键"""
        # 如果有前缀，需要在模式中包含前缀
        if self._key_prefix:
            full_pattern = f"{self._key_prefix}:{pattern}"
        else:
            full_pattern = pattern

        keys = await self.client.keys(full_pattern)
        return [key.decode() if isinstance(key, bytes) else key for key in keys]

    async def expire(self, key: str, seconds: int) -> bool:
        """设置键的过期时间"""
        return bool(await self.client.expire(self.make_key(key), seconds))

    async def ttl(self, key: str) -> int:
        """获取键的剩余生存时间"""
        return await self.client.ttl(self.make_key(key))

    async def incr(self, key: str, amount: int = 1) -> int:
        """递增键的值"""
        return await self.client.incr(self.make_key(key), amount)

    async def decr(self, key: str, amount: int = 1) -> int:
        """递减键的值"""
        return await self.client.decr(self.make_key(key), amount)

    # --- Hash ---

    async def hset(self, key: str, field: str = None, value: str = None, mapping=None) -> bool:
        return await self.client.hset(self.make_key(key), field, value, mapping=mapping) > 0

    async def hget(self, key: str, field: str) -> Optional[str]:
        return await self.client.hget(self.make_key(key), field)

    async def hmget(self, key: str, fields: List[str]) -> Dict[str, Optional[str]]:
        redis_key = self.make_key(key)
        values = await self.client.hmget(redis_key, fields)
        return dict(zip(fields, values))

    async def hincrby(self, name: str, key: str, amount: int = 1) -> int:
        """实现哈希字段值增减"""
        return await self.client.hincrby(name, key, amount) or 0

    async def hgetall(self, key: str) -> dict:
        return await self.client.hgetall(self.make_key(key))

    async def hdel(self, key: str, fields: List[str]) -> bool:
        return await self.client.hdel(self.make_key(key), *fields) > 0

    # --- ZSet ---

    async def zadd(self, key: str, mapping: dict[str, float]) -> int:
        return await self.client.zadd(self.make_key(key), mapping)

    async def zrange(
        self,
        key: str,
        start: int,
        stop: int,
        desc: bool = False,
        withscores: bool = False,
    ) -> list:
        return (
            await self.client.zrange(
                self.make_key(key), start, stop, desc=desc, withscores=withscores
            )
            or []
        )

    async def zrem(self, key: str, *members: str) -> int:
        return await self.client.zrem(self.make_key(key), *members) or 0

    # --- List ---

    async def lpush(self, key: str, *values: str) -> int:
        return await self.client.lpush(self.make_key(key), *values) or 0

    async def rpush(self, key: str, *values: str) -> int:
        """从右侧推入列表"""
        return await self.client.rpush(self.make_key(key), *values) or 0

    async def lpop(self, key: str) -> Optional[str]:
        """从左侧弹出列表元素"""
        result = await self.client.lpop(self.make_key(key))
        return result.decode() if isinstance(result, bytes) else result

    async def rpop(self, key: str) -> Optional[str]:
        result = await self.client.rpop(self.make_key(key))
        return result.decode() if isinstance(result, bytes) else result

    async def lrange(self, key: str, start: int, stop: int) -> list[str]:
        results = await self.client.lrange(self.make_key(key), start, stop) or []
        return [item.decode() if isinstance(item, bytes) else item for item in results]

    async def llen(self, key: str) -> int:
        """获取列表长度"""
        return await self.client.llen(self.make_key(key)) or 0

    async def ltrim(self, key: str, start: int, stop: int) -> bool:
        """修剪列表，只保留指定范围内的元素"""
        return bool(await self.client.ltrim(self.make_key(key), start, stop))

    # --- Set ---

    async def sadd(self, key: str, *members: str) -> int:
        return await self.client.sadd(self.make_key(key), *members) or 0

    async def srem(self, key: str, *members: str) -> int:
        return await self.client.srem(self.make_key(key), *members) or 0

    async def smembers(self, key: str) -> Set[str]:
        members = await self.client.smembers(self.make_key(key)) or set()
        return {member.decode() if isinstance(member, bytes) else member for member in members}

    async def sismember(self, key: str, member: str) -> bool:
        """检查成员是否在集合中"""
        return bool(await self.client.sismember(self.make_key(key), member))

    async def scard(self, key: str) -> int:
        """获取集合大小"""
        return await self.client.scard(self.make_key(key)) or 0

    # --- script ---
    async def script_load(self, script: str) -> str:
        return await self.client.script_load(script)

    async def evalsha(self, script: str, keys: list, args: list):
        return await self.client.evalsha(script, len(keys), *keys, *args)

    async def eval(self, script: str, keys: list, args: list):
        return await self.client.eval(script, len(keys), *keys, *args)

    # --- 批量操作 ---

    async def mget(self, keys: List[str]) -> List[Optional[str]]:
        """批量获取多个键的值"""
        full_keys = [self.make_key(key) for key in keys]
        results = await self.client.mget(full_keys)
        return [result.decode() if isinstance(result, bytes) else result for result in results]

    async def mset(self, mapping: Dict[str, Any]) -> bool:
        """批量设置多个键值对"""
        full_mapping = {self.make_key(key): value for key, value in mapping.items()}
        return bool(await self.client.mset(full_mapping))

    async def delete_pattern(self, pattern: str) -> int:
        """删除匹配模式的所有键"""
        keys = await self.keys(pattern)
        if keys:
            # 移除前缀，因为delete方法会自动添加前缀
            if self._key_prefix:
                keys = [key.replace(f"{self._key_prefix}:", "", 1) for key in keys]
            return await self.client.delete(*[self.make_key(key) for key in keys])
        return 0

    # --- 数据库操作 ---

    async def flushdb(self) -> bool:
        """清空当前数据库"""
        return bool(await self.client.flushdb())

    async def ping(self) -> bool:
        """测试连接"""
        try:
            result = await self.client.ping()
            return result is True or result == b"PONG"
        except Exception:
            return False

    async def info(self, section: Optional[str] = None) -> Dict[str, Any]:
        """获取Redis服务器信息"""
        return await self.client.info(section)

    # --- 事务操作 ---

    async def watch(self, *keys: str) -> bool:
        """监视键的变化"""
        full_keys = [self.make_key(key) for key in keys]
        return bool(await self.client.watch(*full_keys))

    async def unwatch(self) -> bool:
        """取消监视所有键"""
        return bool(await self.client.unwatch())

    # --- 工具方法 ---

    async def clear_all(self) -> bool:
        """清理所有带前缀的键（谨慎使用）"""
        if self._key_prefix:
            pattern = f"{self._key_prefix}:*"
            return await self.delete_pattern(pattern) > 0
        else:
            # 如果没有前缀，不执行清理操作以避免误删
            return False
