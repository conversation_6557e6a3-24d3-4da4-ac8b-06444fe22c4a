import contextlib
import time
from datetime import timedel<PERSON>
from typing import Async<PERSON>tera<PERSON>, Optional

from neo4j import AsyncDriver, AsyncGraphDatabase, AsyncSession
from neo4j.exceptions import AuthError, ConfigurationError, ServiceUnavailable

from commonlib.configs.storages.persistence.neo4j_config import Neo4jConfig
from commonlib.core.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector, ConnectorParams


class Neo4jConnector(BaseConnector[AsyncDriver, Neo4jConfig]):
    _config_class = Neo4jConfig

    def __init__(
        self,
        initial_params: Optional[ConnectorParams] = None,
    ) -> None:
        super().__init__(
            initial_params or ConnectorParams(heartbeat_interval=timedelta(seconds=30))
        )
        self._connection: Optional[AsyncDriver] = None

    @property
    def name(self) -> str:
        return "Neo4j"

    async def _connect(self) -> bool:
        if not self.config:
            raise RuntimeError(f"{self.name} Config not loaded")
        try:
            # 关闭旧连接（如果存在）
            await self._close()

            # 构建连接
            self._connection: AsyncDriver = AsyncGraphDatabase.driver(
                self.config.NEO4J_URI,
                auth=self.config.NEO4J_AUTH,
                **self.config.NEO4J_POOL_CONFIG,
            )

            # 验证连接
            await self._connection.verify_connectivity()

            app_logger.info(
                f"{self.name} connection established. Host: {self.config.NEO4J_HOST}, "
                f"Database: {self.config.NEO4J_DATABASE}"
            )
            return True

        except AuthError as e:
            app_logger.error(f"{self.name} authentication failed: {e}", exception=True)
            return False
        except ServiceUnavailable as e:
            app_logger.error(f"{self.name} service unavailable: {e}", exception=True)
            return False
        except ConfigurationError as e:
            app_logger.error(f"{self.name} configuration error: {e}", exception=True)
            return False
        except Exception as e:
            app_logger.error(f"{self.name} unexpected error during connection: {e}", exception=True)
            return False

    async def _close(self) -> None:
        """安全关闭 Neo4j 连接"""
        if self._connection:
            try:
                await self._connection.close()
                app_logger.info(
                    f"{self.name} connection closed. Host: {self.config.NEO4J_HOST}, "
                    f"Database: {self.config.NEO4J_DATABASE}"
                )
            except Exception as e:
                app_logger.error(
                    f"Error closing {self.name} connection. Error: {e}", exception=True
                )
        self._connection = None

    async def _perform_heartbeat_check(self) -> bool:
        """执行健康检查"""
        try:
            if self._connection is None:
                return False

            # 使用简单的Cypher查询进行健康检查
            async with self._connection.session(database=self.config.NEO4J_DATABASE) as session:
                result = await session.run("RETURN 1 as health_check")
                record = await result.single()
                if record and record["health_check"] == 1:
                    return True
                else:
                    app_logger.error(f"Invalid {self.name} heartbeat response")
                    return False

        except ServiceUnavailable as e:
            app_logger.error(
                f"{self.name} health check failed - service unavailable: {e}", exception=True
            )
            return False
        except Exception as e:
            app_logger.error(f"{self.name} unexpected heartbeat error: {e}", exception=True)
            return False

    @contextlib.asynccontextmanager
    async def session(self, database: Optional[str] = None) -> AsyncIterator[AsyncSession]:
        """
        获取Neo4j会话的上下文管理器

        Args:
            database: 可选的数据库名称，默认使用配置中的数据库
        """
        start_time = time.monotonic()
        db_name = database or self.config.NEO4J_DATABASE

        if not self._connection:
            raise ConnectionError(f"{self.name} connection not available")

        session: AsyncSession = self._connection.session(database=db_name)
        try:
            yield session
            app_logger.debug(
                f"Neo4j会话完成 [session={id(session)}] [database={db_name}] "
                f"[耗时={(time.monotonic() - start_time) * 1000:.2f}ms]"
            )
        except Exception as e:
            app_logger.error(
                f"{self.name} session error in database {db_name}. Error: {e}", exception=True
            )
            raise
        finally:
            await session.close()

    @contextlib.asynccontextmanager
    async def transaction(self, database: Optional[str] = None) -> AsyncIterator[AsyncSession]:
        """
        获取Neo4j事务会话的上下文管理器

        Args:
            database: 可选的数据库名称，默认使用配置中的数据库
        """
        start_time = time.monotonic()
        db_name = database or self.config.NEO4J_DATABASE

        if not self._connection:
            raise ConnectionError(f"{self.name} connection not available")

        async with self._connection.session(database=db_name) as session:
            async with session.begin_transaction() as tx:
                try:
                    yield tx
                    app_logger.debug(
                        f"Neo4j事务完成 [transaction={id(tx)}] [database={db_name}] "
                        f"[耗时={(time.monotonic() - start_time) * 1000:.2f}ms]"
                    )
                except Exception as e:
                    app_logger.error(
                        f"{self.name} transaction error in database {db_name}. Error: {e}",
                        exception=True,
                    )
                    raise


async def provide_neo4j_session(
    connector: Neo4jConnector, database: Optional[str] = None
) -> AsyncSession:
    """
    提供Neo4j会话的依赖注入函数

    Args:
        connector: Neo4j连接器实例
        database: 可选的数据库名称

    Returns:
        AsyncSession: Neo4j异步会话
    """
    async with connector.session(database=database) as session:
        return session
