import contextlib
import time
from datetime import timedelta
from typing import AsyncIterator, Optional

from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from commonlib.configs.storages.persistence.mysql_config import MySQLConfig
from commonlib.core.tsif_logging import app_logger
from commonlib.storages.base import BaseConnector, ConnectorParams


class MySQLConnector(BaseConnector[AsyncEngine, MySQLConfig]):
    _HEALTH_CHECK_SQL = text("SELECT 1")
    _config_class = MySQLConfig

    def __init__(
        self,
        initial_params: Optional[ConnectorParams] = None,
    ) -> None:
        super().__init__(
            initial_params or ConnectorParams(heartbeat_interval=timedelta(seconds=30))
        )
        self._connection: Optional[AsyncEngine] = None
        self._session_factory: Optional[sessionmaker] = None

    @property
    def name(self) -> str:
        return "MySQL"

    async def _connect(self) -> bool:
        if not self.config:
            raise RuntimeError(f"{self.name} Config not loaded")
        try:
            # 关闭旧连接（如果存在）
            await self._close()
            # 构建连接
            self._connection: AsyncEngine = create_async_engine(
                str(self.config.DSN),
                **self.config.MYSQL_POOL_CONFIG,
            )
            self._session_factory = sessionmaker(
                self._connection, class_=AsyncSession, expire_on_commit=False
            )

            return True
        except Exception as e:
            app_logger.error(f"{self.name} SQLAlchemy error during connection: {e}", exception=True)
            return False

    async def _close(self) -> None:
        """安全关闭 MySQL 连接池"""
        if self._connection:
            try:
                await self._connection.dispose()
                app_logger.info(
                    f"{self.name} connection closed. Host: {self.config.MYSQL_HOST}, Database: {self.config.MYSQL_DATABASE}"
                )
            except Exception as e:
                app_logger.error(
                    f"Error closing {self.name} connection. Error: {e}", exception=True
                )

        if self._session_factory:
            try:
                self._session_factory.close_all()
            except Exception as e:
                app_logger.error(f"Error closing {self.name} session. Error: {e}", exception=True)
        self._connection = None
        self._session_factory = None

    async def _perform_heartbeat_check(self) -> bool:
        """执行健康检查"""
        try:
            if self._connection is None:
                return False
            async with self._connection.connect() as conn:
                result = await conn.execute(self._HEALTH_CHECK_SQL)
                if not result.scalar() == 1:  # 验证返回值
                    app_logger.error(f"Invalid {self.name} heartbeat response")
                    return False
            return True
        except SQLAlchemyError as e:
            app_logger.error(f"{self.name} health check failed. Error: {e}", exception=True)
            return False
        except Exception as e:
            app_logger.error(f"{self.name} Unexpected heartbeat error: {e}", exception=True)
            return False

    @contextlib.asynccontextmanager
    async def session(self) -> AsyncIterator[AsyncSession]:
        start_time = time.monotonic()
        session: AsyncSession = self._session_factory()
        try:
            async with session.begin():  # 事务开启
                yield session
                app_logger.debug(
                    f"事务完成 [session={id(session)}] [耗时={(time.monotonic() - start_time) * 1000:.2f}ms]"
                )
                return
        except Exception as e:
            app_logger.error(
                f"{self.name} database error in transaction. Error: {e}", exception=True
            )
            try:
                await session.rollback()  # 必须在事务外部调用 rollback()
            except Exception as rollback_err:
                app_logger.error(
                    f"{self.name} rollback failed. Error: {rollback_err}",
                    exception=True,
                )
            raise
        finally:
            await session.close()


async def provide_mysql_session(
    connector: MySQLConnector,
) -> AsyncSession:
    async with connector.session() as session:
        return session
