"""
简化的应用工厂

替代复杂的DI容器，提供简单直接的FastAPI应用创建和管理
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncIterator, Callable, Dict, List, Optional

from fastapi import APIRouter, FastAPI

from commonlib.core.app_config import AppConfig, setup_app_config
from commonlib.core.app_connections import setup_connection_manager, shutdown_connection_manager
from commonlib.core.tsif_logging import app_logger
from commonlib.utils.scheduler_tasks_context import scheduler_tasks_mgm
from domain_common.app_builder.default_app_builder import AppBuilder


class SimpleAppFactory:
    """简化的应用工厂

    提供FastAPI应用的创建、配置和生命周期管理
    """

    def __init__(
        self,
        config: Optional[AppConfig] = None,
    ):
        """初始化应用工厂

        Args:
            config: 应用配置
        """
        self.config = config
        self._startup_hooks: List[Callable] = []
        self._shutdown_hooks: List[Callable] = []

    def add_startup_hook(self, hook: Callable):
        """添加启动钩子

        Args:
            hook: 启动时执行的函数
        """
        self._startup_hooks.append(hook)

    def add_shutdown_hook(self, hook: Callable):
        """添加关闭钩子

        Args:
            hook: 关闭时执行的函数
        """
        self._shutdown_hooks.append(hook)

    @asynccontextmanager
    async def lifespan(self, app: FastAPI) -> AsyncIterator[None]:
        """应用生命周期管理"""
        try:
            # 启动阶段
            app_logger.info("Starting application...")

            await setup_connection_manager(self.config)
            # 执行启动钩子
            for hook in self._startup_hooks:
                try:
                    if asyncio.iscoroutinefunction(hook):
                        await hook()
                    else:
                        hook()
                except Exception as e:
                    app_logger.error(f"Startup hook failed: {e}")

            # 启动调度器
            AppBuilder.start_app_scheduler()

            app_logger.info("Application started successfully")

            yield

        finally:
            # 停止调度器
            try:
                scheduler_tasks_mgm.shutdown()
            except Exception as e:
                app_logger.warning(f"Error stopping scheduler: {e}")
            # 关闭阶段
            await shutdown_connection_manager()
            app_logger.info("Shutting down application...")

            # 执行关闭钩子
            for hook in reversed(self._shutdown_hooks):
                try:
                    if asyncio.iscoroutinefunction(hook):
                        await hook()
                    else:
                        hook()
                except Exception as e:
                    app_logger.error(f"Shutdown hook failed: {e}")

            app_logger.info("Application shutdown completed")

    def create_app(
        self,
        title: Optional[str] = None,
        description: Optional[str] = None,
        version: str = "1.0.0",
        routers: Optional[Dict[str, APIRouter]] = None,
        router_prefix: str = "/api/v1",
        include_health_check: bool = True,
        **fastapi_kwargs,
    ) -> FastAPI:
        """创建FastAPI应用

        Args:
            title: 应用标题
            description: 应用描述
            version: 应用版本
            routers: 路由字典 {路径: 路由器}
            router_prefix: 路由前缀
            include_health_check: 是否包含健康检查
            **fastapi_kwargs: FastAPI的其他参数

        Returns:
            FastAPI应用实例
        """
        # 使用配置中的默认值
        if self.config and self.config.is_initialized():
            app_config = self.config.application_config
            if app_config:
                title = title or app_config.title
                description = description or app_config.description
                fastapi_kwargs.setdefault("docs_url", app_config.docs_url)
                fastapi_kwargs.setdefault("openapi_url", app_config.openapi_url)
                fastapi_kwargs.setdefault("redoc_url", app_config.redoc_url)

        # 创建FastAPI应用
        self._app = FastAPI(
            title=title or "TSIF Micro Service",
            description=description or "A microservice built with TSIF framework",
            version=version,
            lifespan=self.lifespan,
            **fastapi_kwargs,
        )

        # 注册路由
        if routers:
            AppBuilder.register_routers(
                application=self._app, routers=routers, prefix=router_prefix
            )

        # 注册健康检查
        if include_health_check:
            AppBuilder.register_health_router(application=self._app, prefix=router_prefix)

        app_logger.info(f"FastAPI application created: {title}")
        return self._app


def create_simple_app(
    config: Optional[AppConfig] = None,
    routers: Optional[Dict[str, APIRouter]] = None,
    startup_hooks: Optional[List[Callable]] = None,
    shutdown_hooks: Optional[List[Callable]] = None,
    **app_kwargs,
) -> FastAPI:
    """创建简化的FastAPI应用（便捷函数）

    Args:
        config: 应用配置
        routers: 路由字典
        startup_hooks: 启动钩子列表
        shutdown_hooks: 关闭钩子列表
        **app_kwargs: FastAPI应用参数

    Returns:
        FastAPI应用实例
    """
    app_config = setup_app_config(config, **app_kwargs)
    factory = SimpleAppFactory(config=app_config)

    # 添加钩子
    if startup_hooks:
        for hook in startup_hooks:
            factory.add_startup_hook(hook)

    if shutdown_hooks:
        for hook in shutdown_hooks:
            factory.add_shutdown_hook(hook)

    return factory.create_app(routers=routers, **app_kwargs)
