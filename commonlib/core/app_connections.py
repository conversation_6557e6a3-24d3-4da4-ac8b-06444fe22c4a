"""
简化的连接管理器

替代复杂的DI容器，提供简单直接的数据库连接管理
"""

from commonlib.core.app_config import AppConfig
from commonlib.core.tsif_logging import app_logger
from commonlib.interface.infra_redis.decorators.cache import RedisCacheDecorator
from commonlib.interface.infra_redis.decorators.token_bucket import TokenBucketDecorator
from commonlib.interface.infra_redis.scripts.script import RedisScriptManager
from commonlib.storages.connect_manager import ConnectionManager
from commonlib.storages.persistence.redis.repository import RedisRepository
from commonlib.storages.populate_connectors import populate_connectors
from commonlib.utils.singleton import SingletonMeta


class AppConnectionManager(metaclass=SingletonMeta):
    """简化的连接管理器

    提供数据库连接的创建、管理和清理功能
    """

    def __init__(self):
        self.config: AppConfig | None = None
        self._connection_manager = None
        self._initialized = False

    async def initialize(self, config: AppConfig):
        """初始化连接管理器"""
        self.config = config

        try:
            # 填充连接器注册表
            connector_registry = populate_connectors(config=self.config.connection_priority_config)

            # 初始化连接管理器
            self._connection_manager = ConnectionManager(
                config=self.config.persistence_config,
                connector_registry=connector_registry,
            )

            self._initialized = True
            app_logger.info("Connection manager initialized successfully")

        except Exception as e:
            app_logger.error(f"Failed to initialize connection manager: {e}")
            raise

    async def start(self):
        await self._connection_manager.connect_all()

    async def stop(self):
        await self._connection_manager.close_all()

    @property
    def redis_client(self):
        """获取Redis客户端

        Returns:
            Redis客户端实例
        """

        return self._connection_manager.get_connector("redis")

    @property
    def mysql_client(self):
        """获取MySQL客户端

        Returns:
            MySQL客户端实例
        """

        return self._connection_manager.get_connector("mysql")

    @property
    def postgres_client(self):
        """获取PostgreSQL客户端

        Returns:
            PostgreSQL客户端实例
        """

        return self._connection_manager.get_connector("postgres")

    @property
    def mongodb_client(self):
        """获取MongoDB客户端

        Returns:
            MongoDB客户端实例
        """

        return self._connection_manager.get_connector("mongodb")

    @property
    def rabbitmq_client(self):
        """获取RabbitMQ客户端

        Returns:
            RabbitMQ客户端实例
        """

        return self._connection_manager.get_connector("rabbitmq")

    @property
    def faststream_client(self):
        """获取RabbitMQ客户端

        Returns:
            RabbitMQ客户端实例
        """

        return self._connection_manager.get_connector("faststream")

    def decorator_redis_repo(self):
        return RedisRepository(redis_connector=self.redis_client, key_prefix=self.config.app_name)

    def redis_script_manager(self):
        return RedisScriptManager(
            redis_repo=self.decorator_redis_repo(),
        )

    def redis_cache_decorator(self):
        return RedisCacheDecorator(
            script_manager=self.redis_script_manager(),
        )

    def redis_token_bucket_decorator(self):
        return TokenBucketDecorator(
            script_manager=self.redis_script_manager(),
        )


app_local_connection_manager = AppConnectionManager()


def get_app_connection_manager() -> AppConnectionManager:
    return app_local_connection_manager


async def setup_connection_manager(config: AppConfig) -> AppConnectionManager:
    conn_manager = get_app_connection_manager()
    await conn_manager.initialize(config)
    await conn_manager.start()
    return conn_manager


async def shutdown_connection_manager():
    conn_manager = get_app_connection_manager()
    await conn_manager.stop()
