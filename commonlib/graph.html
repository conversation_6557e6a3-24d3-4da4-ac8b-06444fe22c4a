<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人物关系图谱</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cytoscape/3.21.0/cytoscape.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        #cy {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            text-align: center;
        }
    </style>
</head>
<body>
    <h2>人物关系图谱</h2>
    <div id="cy" style="width: 100%; height: 600px; border: 1px solid #ddd;"></div>

    <script>
        // 数据定义
        const data = {
  "nodes": [
    "张伟",
    "王五",
    "李四",
    "颜莉",
    "怒风",
    "露露",
    "陆仁甲",
    "张三"
  ],
  "edges": [
    {
      "source": "张三",
      "target": "李四",
      "relation": "朋友"
    },
    {
      "source": "王五",
      "target": "李四",
      "relation": "老师"
    },
    {
      "source": "陆仁甲",
      "target": "张伟",
      "relation": "舍友"
    },
    {
      "source": "陆仁甲",
      "target": "张伟",
      "relation": "朋友"
    },
    {
      "source": "陆仁甲",
      "target": "张伟",
      "relation": "拥有"
    },
    {
      "source": "陆仁甲",
      "target": "颜莉",
      "relation": "单方面爱慕"
    },
    {
      "source": "陆仁甲",
      "target": "颜莉",
      "relation": "同学"
    },
    {
      "source": "陆仁甲",
      "target": "露露",
      "relation": "队友"
    },
    {
      "source": "陆仁甲",
      "target": "怒风",
      "relation": "队友"
    },
    {
      "source": "露露",
      "target": "怒风",
      "relation": "队友"
    }
  ]
};
        const elements = [];

        // 处理节点
        data.nodes.forEach(n => {
            elements.push({
                data: {
                    id: n,
                    label: n
                }
            });
        });

        // 处理边
        data.edges.forEach(e => {
            elements.push({
                data: {
                    id: e.source + "_" + e.target,
                    source: e.source,
                    target: e.target,
                    label: e.relation || ''
                }
            });
        });

        // 初始化Cytoscape
        const cy = cytoscape({
            container: document.getElementById('cy'),
            elements: elements,
            style: [
                {
                    selector: 'node',
                    style: {
                        'label': 'data(label)',
                        'background-color': '#0074D9',
                        'color': '#fff',
                        'text-valign': 'center',
                        'text-halign': 'center',
                        'font-size': '12px',
                        'width': 'label',
                        'height': 'label',
                        'padding': '8px'
                    }
                },
                {
                    selector: 'edge',
                    style: {
                        'label': 'data(label)',
                        'line-color': '#666',
                        'target-arrow-color': '#666',
                        'target-arrow-shape': 'triangle',
                        'arrow-scale': 1.5,
                        'curve-style': 'bezier',
                        'font-size': '10px',
                        'color': '#333'
                    }
                }
            ],
            layout: {
                name: 'cose',
                idealEdgeLength: 100,
                nodeOverlap: 20,
                refresh: 20,
                fit: true,
                padding: 30,
                randomize: false,
                componentSpacing: 100,
                nodeRepulsion: 400000,
                edgeElasticity: 100,
                nestingFactor: 5,
                gravity: 80,
                numIter: 1000,
                initialTemp: 200,
                coolingFactor: 0.95,
                minTemp: 1.0
            }
        });
    </script>
</body>
</html>