import json
import os
import sys
from pathlib import Path
from typing import List, Optional, Set, Union

from loguru import logger
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from commonlib.configs.frameworks.fastapi_config import FastApiSettings
from commonlib.configs.services.connector_priority.connector_priority_config import (
    ConnectorPriorityConfig,
)
from commonlib.configs.services.middleware.middleware_configs import MiddlewareConfig
from commonlib.configs.storages.persistence.mongodb_config import MongoDBConfig
from commonlib.configs.storages.persistence.mysql_config import MySQLConfig
from commonlib.configs.storages.persistence.neo4j_config import Neo4jConfig
from commonlib.configs.storages.persistence.postgres_config import PostgresConfig
from commonlib.configs.storages.persistence.redis_config import RedisConfig
from commonlib.configs.storages.queue.celery_config import CeleryConfig
from commonlib.configs.storages.queue.faststream_config import FastStreamConfig
from commonlib.configs.storages.queue.message_broker_config import MessageBrokerConfig, WorkerConfig
from commonlib.configs.storages.queue.rabbitmq_config import RabbitMQConfig


class PersistenceConfig(BaseSettings):
    redis: RedisConfig = Field(default_factory=RedisConfig)
    mysql: MySQLConfig = Field(default_factory=MySQLConfig)
    postgres: PostgresConfig = Field(default_factory=PostgresConfig)
    mongodb: MongoDBConfig = Field(default_factory=MongoDBConfig)
    neo4j: Neo4jConfig = Field(default_factory=Neo4jConfig)
    rabbitmq: RabbitMQConfig = Field(default_factory=RabbitMQConfig)
    celery: CeleryConfig = Field(default_factory=CeleryConfig)
    faststream: FastStreamConfig = Field(default_factory=FastStreamConfig)
    message_broker: MessageBrokerConfig = Field(default_factory=MessageBrokerConfig)
    worker: WorkerConfig = Field(default_factory=WorkerConfig)


class BasicConfig(BaseSettings):
    """Main application configuration with integrated feature flags."""

    # 应用基础配置
    basic_log_dir: Optional[str] = "local_logs"

    # 应用配置组
    application: FastApiSettings = Field(
        default_factory=FastApiSettings, description="FastAPI application settings"
    )
    middleware: MiddlewareConfig = Field(
        default_factory=MiddlewareConfig, description="Middleware configurations"
    )
    # 连接优先级配置
    connection_priority: ConnectorPriorityConfig = Field(
        default_factory=ConnectorPriorityConfig,
        description="ConnectorPriorityConfig configurations",
    )
    # 业务连接配置
    persistence: PersistenceConfig = Field(
        default_factory=PersistenceConfig,
        description="Database and cache configurations",
    )

    # 运行时环境判断属性
    @property
    def is_celery(self) -> bool:
        return "celery" in " ".join(sys.argv).lower()

    @property
    def is_test(self) -> bool:
        return os.getenv("RUN_MODE") == "test"

    @property
    def is_cli(self) -> bool:
        return os.getenv("RUN_MODE") == "cli"

    @property
    def is_arq(self) -> bool:
        return os.getenv("RUN_MODE") == "arq"

    @property
    def log_dir(self) -> str:
        basic_log_dir = self.basic_log_dir
        if self.is_cli:
            final_log_dir = f"{basic_log_dir}/cli"
        elif self.is_celery:
            final_log_dir = f"{basic_log_dir}/celery"
        elif self.is_test:
            final_log_dir = f"{basic_log_dir}/test"
        elif self.is_arq:
            final_log_dir = f"{basic_log_dir}/arq"
        elif self.application.project_name:
            dir_name = self.application.project_name.strip().replace(" ", "").lower() or "app"
            final_log_dir = f"{basic_log_dir}/{dir_name}"
        else:
            final_log_dir = f"{basic_log_dir}/app"
        return final_log_dir

    def export_to_file(self, file_path: Path) -> None:
        """Safely export configs to JSON file."""
        if not file_path.parent.exists():
            file_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            file_path.write_text(self.model_dump_json(indent=2, exclude_unset=True))
            logger.info(f"Configuration exported to {file_path}")
        except Exception as e:
            logger.error(f"Config export failed: {e}")
            raise

    def from_file(self, file_path: Union[str, Path]) -> "BasicConfig":
        """Load configuration from file with validation."""
        path = Path(file_path) if isinstance(file_path, str) else file_path

        if not path.exists():
            raise FileNotFoundError(f"配置文件不存在: {path}")
        try:
            self.__dict__.clear()
            data = json.loads(path.read_text(encoding="utf-8"))
            validated = self.model_validate(data)
            self.__dict__.update(validated.__dict__)
            logger.info(f"成功从JSON文件加载配置: {path}")
            return self
        except (IOError, json.JSONDecodeError) as e:
            logger.error(
                f"JSON文件格式错误 {path}: {e}",
            )
            raise
        except Exception as e:
            logger.error(f"配置加载失败 {path}: {e}")
            raise ValueError(f"配置验证失败: {e}") from e

    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> "BasicConfig":
        """类方法：从文件加载配置"""
        config = cls()
        return config.from_file(file_path)

    @classmethod
    def load_with_search(
        cls,
        config_path: Optional[Union[str, Path]] = None,
        additional_paths: Optional[List[Union[str, Path]]] = None,
    ) -> "BasicConfig":
        """类方法：搜索并加载配置文件"""
        search_paths = cls._get_search_paths(config_path, additional_paths)

        for path in search_paths:
            if path.exists():
                try:
                    return cls.load_from_file(path)
                except Exception as e:
                    logger.warning(f"配置文件加载失败 {path}: {e}")
                    continue

        # 如果没有找到配置文件，返回默认配置
        logger.info("未找到配置文件，使用默认配置")
        return cls()

    @classmethod
    def _get_search_paths(
        cls,
        config_path: Optional[Union[str, Path]] = None,
        additional_paths: Optional[List[Union[str, Path]]] = None,
    ) -> List[Path]:
        """获取配置文件搜索路径"""
        search_paths: List[Path] = []

        if config_path:
            search_paths.append(Path(config_path) if isinstance(config_path, str) else config_path)
        if "CONFIG_FILE_PATH" in os.environ:
            search_paths.append(Path(os.environ["CONFIG_FILE_PATH"]))

        if additional_paths:
            search_paths.extend(Path(p) if isinstance(p, str) else p for p in additional_paths)

        search_paths.extend(
            [
                Path("config.json"),
                Path("configs/config.json"),
                Path("../configs/config.json"),
                Path("../../configs/config.json"),
                Path("../../../configs/config.json"),
            ]
        )

        # 去重并保留顺序
        seen_paths: Set[Path] = set()
        return [
            p
            for p in search_paths
            if not (p.resolve() in seen_paths or seen_paths.add(p.resolve()))
        ]

    model_config = SettingsConfigDict(
        # read from dotenv format configs file
        env_file=".env",
        env_file_encoding="utf-8",
        # ignore extra attributes
        extra="allow",
        # support JSON file loading
        json_file_encoding="utf-8",
    )
