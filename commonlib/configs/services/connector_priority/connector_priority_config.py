from typing import Dict

from pydantic import BaseModel, ConfigDict, Field


class ConnectionPriority(BaseModel):
    enabled: bool = Field(
        default=False,
        description="是否启用该连接器，默认启用",
        json_schema_extra={"example": True},
    )
    connect_priority: int = Field(
        default=5,
        gt=0,
        le=10,
        description="连接优先级(1-10)，数值越小优先级越高",
        json_schema_extra={"example": 1},
    )
    shutdown_priority: int = Field(
        default=5,
        gt=0,
        le=10,
        description="关闭优先级(1-10)，数值越小越先关闭",
        json_schema_extra={"example": 5},
    )

    model_config = ConfigDict(
        frozen=True,  # 防止意外修改
        json_schema_extra={
            "example": {"enabled": True, "connect_priority": 1, "shutdown_priority": 5}
        },
    )


class ConnectorPriorityConfig(BaseModel):
    """连接器优先级配置容器（优化版）"""

    redis: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=1, shutdown_priority=6),
        description="Redis连接配置",
    )
    arq_redis: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=2, shutdown_priority=5),
        description="API限流Redis配置",
    )
    mysql: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=3, shutdown_priority=4),
        description="MySQL连接配置",
    )
    postgres: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=4, shutdown_priority=3),
        description="Postgres连接配置",
    )
    mongodb: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=5, shutdown_priority=2),
        description="MongoDB连接配置",
    )
    rabbitmq: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=6, shutdown_priority=1),
        description="RabbitMQ连接配置",
    )
    faststream: ConnectionPriority = Field(
        default_factory=lambda: ConnectionPriority(connect_priority=5, shutdown_priority=1),
        description="faststream连接配置",
    )

    def active_connectors(self) -> Dict[str, ConnectionPriority]:
        """获取所有启用的连接器配置"""
        _connectors = {
            name: ConnectionPriority(**priority)
            for name, priority in self.model_dump().items()
            if priority.get("enabled", False)
        }
        return {
            item[0]: item[1]
            for item in sorted(_connectors.items(), key=lambda item: item[1].connect_priority)
        }

    model_config = ConfigDict(
        extra="allow",  # 禁止额外字段
        json_schema_extra={
            "example": {
                "redis": {
                    "enabled": True,
                    "connect_priority": 1,
                    "shutdown_priority": 5,
                },
                "mysql": {
                    "enabled": False,
                    "connect_priority": 3,
                    "shutdown_priority": 3,
                },
            }
        },
    )


# 使用示例
if __name__ == "__main__":
    # 1. 默认配置
    custom_config = ConnectorPriorityConfig(
        redis=ConnectionPriority(enabled=False, connect_priority=1, shutdown_priority=5),
        mysql=ConnectionPriority(connect_priority=2, shutdown_priority=4),
    )
    print(custom_config.active_connectors())

    print(custom_config.get_priority("redis"))
