from typing import List

from pydantic import BaseModel, Field, NonNegativeInt


class CORSConfig(BaseModel):
    """CORS 相关配置"""

    AllowOrigins: List[str] = Field(default=["*"], description="允许的来源")
    AllowMethods: List[str] = Field(default=["*"], description="允许的方法")
    AllowHeaders: List[str] = Field(default=["*"], description="允许的请求头")
    AllowCredentials: bool = Field(default=True, description="是否允许携带凭证")
    ExposeHeaders: List[str] = Field(default=[], description="暴露的响应头")
    MaxAge: NonNegativeInt = Field(default=600, description="预检请求的缓存时间")


class HSTSConfig(BaseModel):
    """HTTP 严格传输安全（HSTS）配置"""

    IncludeSubdomains: bool = Field(default=True, description="是否包含子域")
    Preload: bool = Field(default=False, description="是否启用预加载")
    MaxAge: NonNegativeInt = Field(default=31536000, description="HSTS 最大时间")


class SecurityConfig(BaseModel):
    """安全性相关配置"""

    SSLRedirect: bool = Field(default=False, description="是否强制跳转 HTTPS")
    ForceSSL: bool = Field(default=False, description="是否强制 HTTPS 访问")
    FrameDeny: bool = Field(default=True, description="是否启用 X-Frame-Options 保护")
    ContentTypeNosniff: bool = Field(
        default=True, description="是否启用 X-Content-Type-Options 保护"
    )
    BrowserXSSFilter: bool = Field(default=True, description="是否启用 X-XSS-Protection 保护")
    HSTS: HSTSConfig = Field(default_factory=HSTSConfig, description="HSTS 相关配置")


class CompressionConfig(BaseModel):
    """压缩相关配置"""

    Enabled: bool = Field(default=True, description="是否启用压缩")
    Level: NonNegativeInt = Field(default=9, description="压缩等级（1-9）")
    MinimumSize: NonNegativeInt = Field(default=500, description="启用压缩的最小内容大小（字节）")


class MiddlewareConfig(BaseModel):
    """全局中间件配置"""

    cors: CORSConfig = Field(default_factory=CORSConfig, description="CORS 相关配置")
    security: SecurityConfig = Field(default_factory=SecurityConfig, description="安全性相关配置")
    compression: CompressionConfig = Field(
        default_factory=CompressionConfig, description="压缩相关配置"
    )
