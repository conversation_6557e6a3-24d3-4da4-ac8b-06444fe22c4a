from typing import Any, Dict, Optional
from urllib.parse import quote

from pydantic import BaseModel, Field, field_validator


class PostgresConfig(BaseModel):
    """PostgreSQL 配置类"""

    # 连接参数
    POSTGRES_HOST: str = Field(default="localhost", description="PostgreSQL 主机地址")
    POSTGRES_PORT: int = Field(default=5432, description="PostgreSQL 端口")
    POSTGRES_USER: str = Field(default="postgres", description="PostgreSQL 用户名")
    POSTGRES_PASSWORD: str = Field(default="", description="PostgreSQL 密码")
    POSTGRES_DATABASE: str = Field(default="postgres", description="PostgreSQL 数据库名")
    POSTGRES_SCHEMA: Optional[str] = Field(default=None, description="PostgreSQL schema")

    # 连接池配置
    POSTGRES_POOL_SIZE: int = Field(default=5, description="连接池大小")
    POSTGRES_POOL_TIMEOUT: int = Field(default=30, description="连接池超时时间（秒）")
    POSTGRES_POOL_RECYCLE: int = Field(default=1800, description="连接回收时间（秒）")
    POSTGRES_POOL_PRE_PING: bool = Field(default=True, description="是否启用连接前 ping")
    POSTGRES_ECHO: bool = Field(default=False, description="是否启用 SQL 语句日志")

    @property
    def DSN(self) -> str:
        """构建 PostgreSQL DSN 连接字符串"""
        return (
            f"postgresql+asyncpg://{self.POSTGRES_USER}:{quote(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DATABASE}"
        )

    @property
    def POSTGRES_POOL_CONFIG(self) -> Dict[str, Any]:
        """连接池配置"""
        return {
            "pool_size": self.POSTGRES_POOL_SIZE,
            "pool_timeout": self.POSTGRES_POOL_TIMEOUT,
            "pool_recycle": self.POSTGRES_POOL_RECYCLE,
            "pool_pre_ping": self.POSTGRES_POOL_PRE_PING,
            "echo": self.POSTGRES_ECHO,
        }

    @field_validator("POSTGRES_PORT")
    def validate_port(cls, v: int) -> int:
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v

    @field_validator("POSTGRES_POOL_SIZE")
    def validate_pool_size(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Pool size must be greater than 0")
        return v

    @field_validator("POSTGRES_POOL_TIMEOUT")
    def validate_pool_timeout(cls, v: int) -> int:
        if v < 1:
            raise ValueError("Pool timeout must be greater than 0")
        return v
