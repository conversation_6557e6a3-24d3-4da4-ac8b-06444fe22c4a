from typing import Any

from pydantic import BaseModel, ConfigDict, Field, NonNegativeInt


class Neo4jConfig(BaseModel):
    """Neo4j图数据库服务配置"""

    # 基础连接配置
    NEO4J_HOST: str = Field(
        default="localhost",
        description="Neo4j数据库地址",
    )
    NEO4J_PORT: int = Field(default=7687, description="Neo4j数据库端口", ge=1024, le=65535)
    NEO4J_USERNAME: str = Field(
        default="neo4j",
        description="Neo4j用户名",
    )
    NEO4J_PASSWORD: str = Field(
        default="",
        description="Neo4j密码",
    )
    NEO4J_DATABASE: str = Field(
        default="neo4j",
        description="Neo4j数据库名称",
    )
    NEO4J_SCHEME: str = Field(
        default="bolt",
        description="Neo4j连接协议 (bolt, bolt+s, neo4j, neo4j+s)",
    )

    # 连接池配置
    NEO4J_MAX_CONNECTION_POOL_SIZE: NonNegativeInt = Field(
        default=100, description="最大连接池大小", ge=1, le=1000
    )
    NEO4J_MAX_CONNECTION_LIFETIME: NonNegativeInt = Field(
        default=3600, description="连接最大生存时间(秒)", ge=300, le=86400
    )
    NEO4J_CONNECTION_ACQUISITION_TIMEOUT: NonNegativeInt = Field(
        default=60, description="连接获取超时时间(秒)", ge=1, le=300
    )
    NEO4J_CONNECTION_TIMEOUT: NonNegativeInt = Field(
        default=30, description="连接超时时间(秒)", ge=1, le=300
    )
    NEO4J_KEEP_ALIVE: bool = Field(default=True, description="是否启用连接保活")
    NEO4J_ENCRYPTED: bool = Field(default=False, description="是否启用加密连接")
    NEO4J_TRUST: str = Field(
        default="TRUST_ALL_CERTIFICATES",
        description="SSL信任策略 (TRUST_ALL_CERTIFICATES, TRUST_SYSTEM_CA_SIGNED_CERTIFICATES)",
    )

    @property
    def NEO4J_URI(self) -> str:
        """构造Neo4j连接URI"""
        return f"{self.NEO4J_SCHEME}://{self.NEO4J_HOST}:{self.NEO4J_PORT}"

    @property
    def NEO4J_POOL_CONFIG(self) -> dict[str, Any]:
        """连接池配置"""
        return {
            "max_connection_pool_size": self.NEO4J_MAX_CONNECTION_POOL_SIZE,
            "max_connection_lifetime": self.NEO4J_MAX_CONNECTION_LIFETIME,
            "connection_acquisition_timeout": self.NEO4J_CONNECTION_ACQUISITION_TIMEOUT,
            "connection_timeout": self.NEO4J_CONNECTION_TIMEOUT,
            "keep_alive": self.NEO4J_KEEP_ALIVE,
            "encrypted": self.NEO4J_ENCRYPTED,
            "trust": self.NEO4J_TRUST,
        }

    @property
    def NEO4J_AUTH(self) -> tuple[str, str]:
        """认证信息"""
        return self.NEO4J_USERNAME, self.NEO4J_PASSWORD

    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        extra="allow",
    )
