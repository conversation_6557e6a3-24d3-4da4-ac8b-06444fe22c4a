from typing import Any

from pydantic import BaseModel, ConfigDict, Field, MongoDsn, NonNegativeInt


class MongoDBConfig(BaseModel):
    """MongoDB服务配置"""

    # 基础连接配置
    MONGODB_HOST: str = Field(
        default="localhost",
        description="MongoDB地址",
    )
    MONGO_DB: str = Field(
        default="admin",
        description="MongoDB地址",
    )
    MONGODB_PORT: int = Field(default=27017, description="MongoDB端口", ge=1024, le=65535)
    MONGODB_USERNAME: str = Field(
        default="",
        description="MongoDB用户名",
    )
    MONGODB_PASSWORD: str = Field(
        default="",
        description="MongoDB密码",
    )
    MONGODB_AUTH_SOURCE: str = Field(
        default="admin",
        description="认证数据库",
    )
    MONGODB_SCHEME: str = Field(
        default="mongodb",
        description="MongoDB连接协议",
    )

    # 连接池配置
    MONGODB_MIN_POOL_SIZE: NonNegativeInt = Field(
        default=10, description="最小连接数", ge=1, le=100
    )
    MONGODB_MAX_POOL_SIZE: NonNegativeInt = Field(
        default=100, description="最大连接数", ge=1, le=1000
    )

    MONGODB_MAX_IDLE_TIME_MS: NonNegativeInt = Field(
        default=60000, description="最大空闲时间(毫秒)", ge=1000, le=3600000
    )
    MONGODB_CONNECT_TIMEOUT_MS: NonNegativeInt = Field(
        default=20000, description="连接超时时间(毫秒)", ge=1000, le=60000
    )

    @property
    def DSN(self) -> MongoDsn:
        """构造MongoDB DSN连接字符串"""
        return MongoDsn.build(
            scheme=self.MONGODB_SCHEME,
            username=self.MONGODB_USERNAME,
            password=self.MONGODB_PASSWORD,
            host=self.MONGODB_HOST,
            port=self.MONGODB_PORT,
        )

    @property
    def MONGODB_POOL_CONFIG(self) -> dict[str, Any]:
        """连接池配置"""
        return {
            "maxPoolSize": self.MONGODB_MAX_POOL_SIZE,
            "minPoolSize": self.MONGODB_MIN_POOL_SIZE,
            "maxIdleTimeMS": self.MONGODB_MAX_IDLE_TIME_MS,
            "connectTimeoutMS": self.MONGODB_CONNECT_TIMEOUT_MS,
            "authSource": self.MONGODB_AUTH_SOURCE,
        }

    model_config = ConfigDict(
        validate_assignment=True,
        validate_default=True,
        json_encoders={MongoDsn: str},
        extra="allow",
    )
