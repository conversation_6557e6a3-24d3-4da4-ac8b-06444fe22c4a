from pydantic import BaseModel, ConfigDict, Field


class CeleryConfig(BaseModel):
    """Celery 配置类（所有配置项添加 CELERY_ 前缀，确保与 Celery 兼容）"""

    CELERY_BROKER_URL: str = Field(default="", description="消息代理 URL")
    CELERY_RESULT_BACKEND: str = Field(default="", description="结果存储后端")

    # 时区
    CELERY_TIMEZONE: str = Field(default="Asia/Shanghai", description="时区")
    CELERY_ENABLE_UTC: bool = Field(default=True, description="是否启用 UTC")

    model_config = ConfigDict(validate_assignment=True, validate_default=True, extra="allow")
