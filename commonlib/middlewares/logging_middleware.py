import json
import time
import uuid
from typing import Any, Callable, Dict

from fastapi import FastAPI, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from commonlib.core.context import recyclable_app_correlation_id
from commonlib.core.tsif_logging import app_logger

# 最大允许记录的body大小（1kb）
MAX_LOG_BODY_SIZE = 1 * 1024

# 需要跳过日志记录的内容类型
SKIP_LOGGING_CONTENT_TYPES = {
    # Excel 相关
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
    "application/vnd.openxmlformats-officedocument.presentationml.template",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel.sheet.macroEnabled.12",
    "application/vnd.ms-excel.template.macroEnabled.12",
    "application/vnd.ms-excel.addin.macroEnabled.12",
    "application/vnd.ms-excel.sheet.binary.macroEnabled.12",
    # 二进制文件
    "application/octet-stream",
    "application/pdf",
    "application/x-binary",
    # 图片
    "image/png",
    "image/jpeg",
    "image/gif",
    "image/webp",
    "image/tiff",
    "image/bmp",
    # 压缩文件
    "application/zip",
    "application/x-rar-compressed",
    "application/x-tar",
    "application/x-gzip",
    "application/x-bzip2",
    "application/x-7z-compressed",
    # 其他二进制格式
    "application/xml",
    "audio/",
    "video/",
    "font/",
}


class AsyncIteratorWrapper:
    """The following is a utility class that transforms a
    regular iterable to an asynchronous one.

    link: https://www.python.org/dev/peps/pep-0492/#example-2
    """

    def __init__(self, obj):
        self._it = iter(obj)

    def __aiter__(self):
        return self

    async def __anext__(self):
        try:
            value = next(self._it)
        except StopIteration:
            raise StopAsyncIteration
        return value


class LoggingMiddleware(BaseHTTPMiddleware):
    """统一的日志中间件，记录请求和响应信息"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        start_time = time.perf_counter()
        request_id = recyclable_app_correlation_id.get() or uuid.uuid4().hex
        recyclable_app_correlation_id.set(request_id)

        # 检查是否需要跳过日志记录
        content_type = request.headers.get("content-type", "").lower()
        skip_logging = self._should_skip_logging(content_type)

        # 记录请求信息
        try:
            request_dict = {} if skip_logging else await self._log_request(request)
            response = await call_next(request)

            # 即使请求跳过日志，我们仍然需要处理响应以重置迭代器
            response_dict = await self._process_response(response, skip_logging)

            # 计算请求处理时间
            cost_time = time.perf_counter() - start_time

            if not skip_logging:
                # 构建结构化日志
                log_data = {
                    "type": "api_access",
                    "method": request.method,
                    "path": str(request.url.path),
                    "query_params": str(request.query_params),
                    "client_host": request.client.host if request.client else "unknown",
                    "status_code": response.status_code,
                    "cost_time": round(cost_time, 3),
                    "request": request_dict,
                    "response": response_dict,
                    "request_id": request_id,
                }

                # 使用extra参数传递结构化数据
                app_logger.biz(log_data)

            return response

        except Exception as e:
            # 记录异常信息
            error_data = {
                "type": "api_error",
                "method": request.method,
                "path": str(request.url.path),
                "error": str(e),
                "request_id": request_id,
                "cost_time": round(time.perf_counter() - start_time, 3),
            }

            # 使用extra参数传递错误信息
            app_logger.error("API Error:{}".format({"structured_data": error_data}), exception=True)
            # raise

    def _should_skip_logging(self, content_type: str) -> bool:
        """检查是否需要跳过日志记录"""
        if not content_type:
            return False

        return any(
            content_type.startswith(skip_type)
            or (skip_type.endswith("/") and content_type.startswith(skip_type))
            for skip_type in SKIP_LOGGING_CONTENT_TYPES
        )

    async def _log_request(self, request: Request) -> Dict[str, Any]:
        """处理并记录请求信息"""
        request_info = {
            "method": request.method,
            "path": str(request.url.path),
            "headers": self._filter_headers(dict(request.headers.items())),
            "client_host": request.client.host if request.client else "unknown",
            "body": None,
        }

        # 处理请求体
        content_type = request.headers.get("content-type", "").lower()
        try:
            if "application/json" in content_type:
                request_info["body"] = await request.json()
            elif "multipart/form-data" in content_type:
                request_info["body"] = "multipart/form-data content"
            elif "application/x-www-form-urlencoded" in content_type:
                form = await request.form()
                request_info["body"] = dict(form)
        except Exception as e:
            request_info["body"] = f"Error parsing request body: {str(e)}"

        return request_info

    @staticmethod
    def _filter_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """过滤敏感header信息"""
        sensitive_headers = {"authorization", "cookie", "x-src-key"}
        return {
            k: v if k.lower() not in sensitive_headers else "[FILTERED]" for k, v in headers.items()
        }

    async def _process_response(
        self, response: Response, skip_logging: bool = False
    ) -> Dict[str, Any]:
        """处理响应信息"""
        if skip_logging:
            # 对于需要跳过的内容类型，我们仍然需要处理迭代器
            await self._consume_response_body(response)
            return {
                "status_code": response.status_code,
                "headers": self._filter_headers(dict(response.headers.items())),
                "body": "[BINARY CONTENT - SKIPPED LOGGING]",
            }

        response_info = {
            "status_code": response.status_code,
            "headers": self._filter_headers(dict(response.headers.items())),
            "body": None,
        }

        content_type = response.headers.get("content-type", "").lower()

        if "application/json" in content_type:
            try:
                body = []
                async for chunk in response.__dict__["body_iterator"]:
                    body.append(chunk)

                # 重新设置body_iterator以供后续使用
                response.__setattr__("body_iterator", AsyncIteratorWrapper(body))

                # 解析响应体
                if body:
                    content = body[0].decode("utf-8")
                    try:
                        parsed_body = json.loads(content)
                        # 如果响应体太大，只保留部分内容
                        if len(content) > MAX_LOG_BODY_SIZE:
                            response_info["body"] = {
                                "truncated": True,
                                "preview": str(parsed_body)[:MAX_LOG_BODY_SIZE] + "...",
                                "total_length": len(content),
                            }
                        else:
                            response_info["body"] = parsed_body
                    except json.JSONDecodeError:
                        response_info["body"] = {
                            "raw": (
                                content[:MAX_LOG_BODY_SIZE] + "..."
                                if len(content) > MAX_LOG_BODY_SIZE
                                else content
                            )
                        }
            except Exception as e:
                response_info["body"] = f"Error processing response body: {str(e)}"
        else:
            # 对于非JSON响应，我们仍然需要消耗body
            await self._consume_response_body(response)
            response_info["body"] = f"[NON-JSON CONTENT: {content_type}]"

        return response_info

    async def _consume_response_body(self, response: Response):
        """消耗响应体而不处理它"""
        if "body_iterator" in response.__dict__:
            body = []
            async for chunk in response.__dict__["body_iterator"]:
                body.append(chunk)
            response.__setattr__("body_iterator", AsyncIteratorWrapper(body))


def setup_logging_middleware(app: FastAPI, *args, **kwargs) -> None:
    app.add_middleware(LoggingMiddleware)
    app_logger.info("logging middleware configured")
