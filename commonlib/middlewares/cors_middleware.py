from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from commonlib.configs.basic_configs import BasicConfig
from commonlib.core.tsif_logging import app_logger


def setup_cors_middleware(
    app: FastAPI,
    configs: BasicConfig,
) -> None:
    """配置CORS中间件
    allowed_origins: 允许的源列表，默认为 ["*"]
    allowed_methods: 允许的HTTP方法列表
    allowed_headers: 允许的HTTP头列表
    allow_credentials: 是否允许携带凭证
    """
    cors_config = configs.middleware.cors
    allowed_origins = cors_config.AllowOrigins
    allowed_methods = cors_config.AllowMethods
    allowed_headers = cors_config.AllowHeaders
    allow_credentials = cors_config.AllowCredentials
    if not allowed_origins:
        allowed_origins = ["*"]
        app_logger.warning(
            f"No CORS origins specified, defaulting to allow all origins:{allowed_origins}"
        )

    if not allowed_methods:
        allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH", "HEAD"]

    if not allowed_headers:
        allowed_headers = [
            "Authorization",
            "Content-Type",
            "Accept",
            "Origin",
            "X-Requested-With",
            "X-Request-ID",
        ]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=allow_credentials,
        allow_methods=allowed_methods,
        allow_headers=allowed_headers,
        expose_headers=["X-Request-ID"],
        max_age=3600,
    )

    app_logger.info(
        "CORS middleware configured, extra:{}".format(
            {
                "allowed_origins": allowed_origins,
                "allowed_methods": allowed_methods,
                "allowed_headers": allowed_headers,
                "allow_credentials": allow_credentials,
            }
        )
    )
