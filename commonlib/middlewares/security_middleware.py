from typing import Callable, Dict, Optional

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from commonlib.configs.basic_configs import BasicConfig
from commonlib.configs.services.middleware.middleware_configs import SecurityConfig


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    def __init__(
        self,
        app: ASGIApp,
        security_config: SecurityConfig,
        csp_policy: Optional[str] = None,
    ) -> None:
        super().__init__(app)
        self._security_config = security_config
        self._csp_policy = csp_policy or self._default_csp_policy()
        self._security_headers = self._get_security_headers()

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        if request.url.path in ["/docs", "/redoc", "/openapi.json"]:
            return response
        # 动态处理HTTPS相关头
        if request.url.scheme == "https" and self._security_config.HSTS:
            self._apply_hsts_headers(response)

        self._apply_common_headers(response)
        return response

    def _apply_hsts_headers(self, response: Response) -> None:
        """动态生成HSTS头"""
        hsts = self._security_config.HSTS
        directives = [f"max-age={hsts.MaxAge}"]
        if hsts.IncludeSubdomains:
            directives.append("includeSubDomains")
        if hsts.Preload:
            directives.append("preload")
        response.headers["Strict-Transport-Security"] = "; ".join(directives)

    def _apply_common_headers(self, response: Response) -> None:
        """应用通用安全头"""
        headers = {
            "X-Content-Type-Options": (
                "nosniff" if self._security_config.ContentTypeNosniff else None
            ),
            "X-Frame-Options": "DENY" if self._security_config.FrameDeny else None,
            "Content-Security-Policy": self._csp_policy,
            "Referrer-Policy": "strict-origin-when-cross-origin",
        }
        for k, v in headers.items():
            if v:
                response.headers[k] = v

    def _default_csp_policy(self) -> str:
        """默认的CSP策略"""
        return "; ".join(
            [
                "default-src 'self'",
                "img-src 'self' data: https:",
                "style-src 'self' 'unsafe-inline'",
                "script-src 'self'",
                "font-src 'self' data:",
                "object-src 'none'",
                "base-uri 'self'",
                "form-action 'self'",
                "frame-ancestors 'none'",
                "block-all-mixed-content",
                "upgrade-insecure-requests",
            ]
        )

    def _get_security_headers(self) -> Dict[str, str]:
        """获取所有安全头"""
        return {
            # 内容安全策略
            "Content-Security-Policy": self._csp_policy,
            # HTTP严格传输安全
            "Strict-Transport-Security": f"max-age={self._security_config.HSTS.MaxAge}; includeSubDomains; preload",
            # 阻止浏览器猜测内容类型
            "X-Content-Type-Options": "nosniff",
            # 防止点击劫持
            "X-Frame-Options": "DENY",
            # XSS保护
            "X-XSS-Protection": "1; mode=block",
            # 引荐来源策略
            "Referrer-Policy": "strict-origin-when-cross-origin",
            # 权限策略
            "Permissions-Policy": "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()",
        }


def setup_security_middleware(app: FastAPI, configs: BasicConfig) -> None:
    """修正后的配置方法"""
    app.add_middleware(
        SecurityHeadersMiddleware,
        security_config=configs.middleware.security,
        csp_policy=None,  # 可传入自定义CSP
    )
