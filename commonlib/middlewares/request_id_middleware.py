import uuid
from typing import Callable

from fastapi import Fast<PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from commonlib.core.context import RecyclableContextVar, recyclable_app_correlation_id


class RequestIDMiddleware(BaseHTTPMiddleware):
    """从请求头、参数、体中提取 request_id 并注入上下文"""

    HEADER_KEY = "X-Request-ID"
    PARAM_KEY = "request_id"

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        request_id = await self._extract_request_id(request)
        # 注入上下文
        RecyclableContextVar.increment_thread_recycles()
        recyclable_app_correlation_id.set(request_id)

        # 响应头写入
        response = await call_next(request)
        response.headers[self.HEADER_KEY] = request_id
        return response

    async def _extract_request_id(self, request: Request) -> str:
        # 1. 请求头
        request_id = request.headers.get(self.HEADER_KEY)
        if request_id:
            return request_id

        # 2. Query 参数
        if self.PARAM_KEY in request.query_params:
            return request.query_params[self.PARAM_KEY]

        # 3. 自动生成
        return uuid.uuid4().hex


def setup_request_id_middleware(app: FastAPI, *args, **kwargs) -> None:
    """修正后的配置方法"""

    app.add_middleware(
        RequestIDMiddleware,
    )
