"""
认证中间件

处理JWT令牌验证、会话管理等认证相关功能
"""

from typing import Optional

from fastapi import HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from domain_common.security.cache_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from domain_common.security.jwt_manager import <PERSON><PERSON><PERSON>ana<PERSON>, TokenPayload
from domain_common.security.session_manager import SessionManager


class AuthMiddleware:
    """认证中间件"""

    def __init__(
        self,
        jwt_manager: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        session_manager: SessionManager,
        cache_manager: CacheManager,
    ):
        self.jwt_manager = jwt_manager
        self.session_manager = session_manager
        self.cache_manager = cache_manager
        self.security = HTTPBearer(auto_error=False)

    async def authenticate_request(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = None,
    ) -> Optional[TokenPayload]:
        """认证请求"""
        # TODO: 实现请求认证逻辑
        # 1. 提取访问令牌
        # 2. 验证令牌有效性
        # 3. 检查会话状态
        # 4. 验证设备指纹
        # 5. 更新最后活动时间
        # 6. 返回令牌载荷

        if not credentials:
            return None

        # 验证访问令牌
        token_payload = await self.jwt_manager.verify_access_token(credentials.credentials)
        if not token_payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid access token"
            )

        # 验证会话
        device_fingerprint = self._extract_device_fingerprint(request)
        ip_address = self._extract_ip_address(request)

        session_valid = await self.session_manager.validate_session(
            token_payload.session_id, device_fingerprint, ip_address
        )

        if not session_valid:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid session")

        return token_payload

    async def require_authentication(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = None,
    ) -> TokenPayload:
        """要求认证"""
        token_payload = await self.authenticate_request(request, credentials)

        if not token_payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
            )

        return token_payload

    async def require_permission(
        self,
        request: Request,
        resource: str,
        action: str,
        credentials: Optional[HTTPAuthorizationCredentials] = None,
    ) -> TokenPayload:
        """要求特定权限"""
        # TODO: 实现权限检查逻辑
        # 1. 先进行认证
        # 2. 获取用户权限
        # 3. 检查特定权限
        # 4. 缓存权限检查结果
        # 5. 返回令牌载荷

        token_payload = await self.require_authentication(request, credentials)

        # 检查权限
        has_permission = await self._check_permission(
            token_payload.user_id, token_payload.tenant_id, resource, action
        )

        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied: {resource}:{action}",
            )

        return token_payload

    async def require_role(
        self,
        request: Request,
        required_roles: list,
        credentials: Optional[HTTPAuthorizationCredentials] = None,
    ) -> TokenPayload:
        """要求特定角色"""
        token_payload = await self.require_authentication(request, credentials)

        # 检查角色
        user_roles = token_payload.roles
        has_required_role = any(role in user_roles for role in required_roles)

        if not has_required_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Role required: {required_roles}",
            )

        return token_payload

    async def _check_permission(
        self, user_id: str, tenant_id: str, resource: str, action: str
    ) -> bool:
        """检查用户权限"""
        # 先从缓存获取权限检查结果
        cached_result = await self.cache_manager.get_permission_check(
            user_id, tenant_id, resource, action
        )

        if cached_result is not None:
            return cached_result

        # 从缓存获取用户权限
        user_permissions = await self.cache_manager.get_user_permissions(user_id, tenant_id)

        if user_permissions is None:
            # 需要从数据库查询用户权限
            # user_permissions = await self._load_user_permissions(user_id, tenant_id)
            user_permissions = []  # 临时占位

        # 检查权限
        permission_code = f"{resource}:{action}"
        has_permission = permission_code in user_permissions

        # 缓存权限检查结果
        await self.cache_manager.cache_permission_check(
            user_id, tenant_id, resource, action, has_permission
        )

        return has_permission

    def _extract_device_fingerprint(self, request: Request) -> Optional[str]:
        """提取设备指纹"""
        return request.headers.get("X-Device-Fingerprint")

    def _extract_ip_address(self, request: Request) -> Optional[str]:
        """提取IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 从连接信息获取
        if hasattr(request, "client") and request.client:
            return request.client.host

        return None

    def _extract_user_agent(self, request: Request) -> Optional[str]:
        """提取用户代理"""
        return request.headers.get("User-Agent")
