import os
import json
from neo4j import GraphDatabase
from openai import OpenAI


# ========== 配置 ==========
# DeepSeek API
client = OpenAI(
    api_key="***********************************",
    base_url="https://api.deepseek.com/v1"
)

# Neo4j 配置
NEO4J_URI = "neo4j://127.0.0.1:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "thismore@123456"

driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))


# ========== 调用 DeepSeek 抽取关系 ==========
def extract_relations(text: str):
    prompt = f"""
    请从以下文本中抽取出人物关系，严格输出 JSON（不要加 ```json 或其他解释文字）：

    要求格式：
    {{
      "characters": ["张三","李四"],
      "relations": [
        {{"source": "张三", "target": "李四", "relation": "朋友"}}
      ]
    }}

    文本内容：{text}
    """

    resp = client.chat.completions.create(
        model="deepseek-chat",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.2
    )

    return resp.choices[0].message.content


# ========== 写入 Neo4j ==========
def insert_to_neo4j(data: dict):
    with driver.session() as session:
        for c in data["characters"]:
            session.run("MERGE (p:Person {name: $name})", name=c)
        for r in data["relations"]:
            session.run("""
                MATCH (a:Person {name: $source}), (b:Person {name: $target})
                MERGE (a)-[:RELATION {type: $relation}]->(b)
            """, source=r["source"], target=r["target"], relation=r["relation"])


# ========== 从 Neo4j 读取数据 ==========
def get_graph_from_neo4j():
    query = """
    MATCH (a:Person)-[r:RELATION]->(b:Person)
    RETURN a.name AS source, b.name AS target, r.type AS relation
    """
    results = []
    nodes = set()
    with driver.session() as session:
        for record in session.run(query):
            results.append({
                "source": record["source"],
                "target": record["target"],
                "relation": record["relation"]
            })
            nodes.add(record["source"])
            nodes.add(record["target"])
    return {"nodes": list(nodes), "edges": results}


import json


def generate_html(graph_data, filename="graph.html"):
    # 确保数据格式正确
    if not isinstance(graph_data, dict):
        raise ValueError("graph_data 必须是字典格式")

    if 'nodes' not in graph_data or 'edges' not in graph_data:
        raise ValueError("graph_data 必须包含 'nodes' 和 'edges' 键")

    # 使用ensure_ascii=False保留中文，并添加meta标签
    graph_data_json = json.dumps(graph_data, ensure_ascii=False, indent=2)

    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人物关系图谱</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cytoscape/3.21.0/cytoscape.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        #cy {{
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h2 {{
            color: #333;
            text-align: center;
        }}
    </style>
</head>
<body>
    <h2>人物关系图谱</h2>
    <div id="cy" style="width: 100%; height: 600px; border: 1px solid #ddd;"></div>

    <script>
        // 数据定义
        const data = {graph_data_json};
        const elements = [];

        // 处理节点
        data.nodes.forEach(n => {{
            elements.push({{
                data: {{
                    id: n,
                    label: n
                }}
            }});
        }});

        // 处理边
        data.edges.forEach(e => {{
            elements.push({{
                data: {{
                    id: e.source + "_" + e.target,
                    source: e.source,
                    target: e.target,
                    label: e.relation || ''
                }}
            }});
        }});

        // 初始化Cytoscape
        const cy = cytoscape({{
            container: document.getElementById('cy'),
            elements: elements,
            style: [
                {{
                    selector: 'node',
                    style: {{
                        'label': 'data(label)',
                        'background-color': '#0074D9',
                        'color': '#fff',
                        'text-valign': 'center',
                        'text-halign': 'center',
                        'font-size': '12px',
                        'width': 'label',
                        'height': 'label',
                        'padding': '8px'
                    }}
                }},
                {{
                    selector: 'edge',
                    style: {{
                        'label': 'data(label)',
                        'line-color': '#666',
                        'target-arrow-color': '#666',
                        'target-arrow-shape': 'triangle',
                        'arrow-scale': 1.5,
                        'curve-style': 'bezier',
                        'font-size': '10px',
                        'color': '#333'
                    }}
                }}
            ],
            layout: {{
                name: 'cose',
                idealEdgeLength: 100,
                nodeOverlap: 20,
                refresh: 20,
                fit: true,
                padding: 30,
                randomize: false,
                componentSpacing: 100,
                nodeRepulsion: 400000,
                edgeElasticity: 100,
                nestingFactor: 5,
                gravity: 80,
                numIter: 1000,
                initialTemp: 200,
                coolingFactor: 0.95,
                minTemp: 1.0
            }}
        }});
    </script>
</body>
</html>"""

    with open(filename, "w", encoding="utf-8") as f:
        f.write(html_template)
    print(f"✅ 已生成 {filename}，请用浏览器打开查看图谱")





# ========== 主流程 ==========
if __name__ == "__main__":
    sample_text = """
在校长吼完之后，浑身爆发出火焰冲天而起，随后一发炽热的火线对着天上坠落的虫族卵囊焚烧而去。

校场上学生十不存一，喊出一级警戒后，剩下的学生全都开始做出了规避动作。

指挥系的学生身边的工农一号机械人开始运作，很快十几个临时堡垒建立了起来。

但也仅此而已，因为指挥官需要能晶，那是一种纯净的能量，将能晶带入指挥堡垒，才能够进行机械士兵的生产。

现在的这种情况明显是无法给他们能晶补给的。

老师们也反应了过来，指挥着战士们朝着校场能源储备库前进。

“基因库！！警戒！敌人目标是生物引擎！！绝不能让虫族接触到生物引擎！！”

天空上，少将校长视野更为开阔，他注意到更多的虫族卵囊朝着基因库的方向坠落。

那边是基因药剂和生物引擎的储藏地，是虫族的首选进攻目标。

陆仁甲看了一眼身边的张伟，咬牙起身就往基因库方向跑。

此刻没有时间悲伤，作为军校学生，战争的时候，他就是列兵。

哪怕他不知道自己能做什么，但这一刻他是人族士兵。

奔跑的同时，陆仁甲抬起手臂，他的制式臂甲弹出呼叫窗口：

“张伟！找我集合！”

教学楼，工农一号充能停放区，一台最基础型号的工农一号灯光亮起，随后它身后的推进器喷射，冲破了玻璃朝着陆仁甲的方位靠近。

张伟，是陆仁甲给自己的工农一号取的名字。

而张伟的工农一号，名字叫做路人甲。

“轰！！”

第一批虫族卵囊坠落地面，这是虫族的惯用登陆技巧，也是只适用于虫族特殊结构的登陆方式。

“piapia——”

黏糊糊的破茧声响起，陆仁甲连忙停下脚步。

在他前方，是十只浑身带着粘液刚刚孵化的虫子。

这些虫子浑身安静，生有八足，前肢更是带着锋利的锯齿，每一个都有成年哈士奇大小。

虫族基础兵种，刀虫。

这些虫子浑身带着腥臭的味道，嗜血的豆豆眼瞬间锁定了陆仁甲。

它们刚刚着陆，正是饥饿需要捕食的时候，虫族的食欲，是星空里面最旺盛的。

“艹！”

陆仁甲此刻并无任何武器，哪怕连一把手枪都没有。

赤手空拳，没有基因强化过的他不可能是这些刀虫的对手。

而张伟也刚好飞到了陆仁甲身边，不过陆仁甲来不及取出武器了。

因为这里是，军校。

“杀！！”

战士系的学长开始冲锋，他们速度惊人，手里的武器各种各样。

八位学长学姐从陆仁甲后面冲了出来，一个照面就打烂了刀虫的脑袋，绿色的浆液四溅。

军校这些毕业的学长学姐可不是憨货，都是经历过实战回来的。

除了一开始被打了个措手不及，反应过来依旧很迅速。

“工农一号?指挥系的?学弟，快去基因库！”

领头的壮汉穿着战术短袖，手里扛着一把大刀对着陆仁甲喊着。

陆仁甲点头，没有犹豫带着张伟继续往前冲，指挥官需要资源，当一个指挥官资源足够，他的战力将会远超战士体系。

很快，基因库就到了眼前，可陆仁甲呆滞了。

因为，一个穿着军服的老师，正把手穿透了另一位老师的胸口，他的手掌覆盖了一层鳞片。

“*@*.....”

不明意义的音符从它嘴里穿穿，僵硬的扭头，陆仁甲看到了它脸上眼睛，耳朵，鼻子，还有嘴角伸出来的诡异触须。

特别是耳朵和眼睛的触须，还在往下滴落着红白的半凝固物质：

“控心虫....”

控心虫，虫族高阶兵种，可以寄生在人体，操控身体办事。

而控心虫的出现最恐怖的事情，就是意味着虫族母皇的到来。

它，可以理解为虫族母皇的左右手。

这也能说明防护最强的军校，为何防护网没有打开，因为控心虫从早就心控了一位老师，他把防护程序全部关闭了。

“啪嗒....”

被洞穿胸口的老师坠落，而控心虫操控的尸体嘴里依旧冒出着诡异的音节朝着陆仁甲走来。

陆仁甲这时候也看见了基因库门内一片尸体，所有来支援的老师和学生全都被他杀了。

这一刻，陆仁甲大脑高速运转，精神力突破了阈值。

他只是一介无名小兵，虫族母皇不可能只是针对他，就算要杀他，随手一个触手或者鞭刺就能要了他的命。

终于，陆仁甲看到了他前方十米的位置，有一个保险箱，上面有【极度机密】的签封。

【生物引擎，它的目标是生物引擎！?】

控心虫已经伸出了手，它手掌飚出血液，一条带着毒刺的触手伸出，准备去勾箱子。

“砰！”

枪声传来，一发子弹直接打爆了控心虫的脑袋。

也是这一刻，陆仁甲当机立断冲锋上前捡起箱子就往回跑：

“是控心虫！爆头死不了！”

在他身后，开枪的正是他女神，颜莉。

听到控心虫，颜莉表情微变，刚刚倒下的尸体再度站了起来。

她果断拉栓，又是一枪打了过去，打断了它刚刚伸出来的触手，同时大喊：

“跑！去机舱！离开边陲星！不能让它们融合引擎！”

军人的职责，是听令。

陆仁甲不搞扭捏那一套，头也不回的抱着箱子又开始了奔跑。

他与颜莉擦身而过，颜莉目光坚定，端着大狙对着陆仁甲身后，陆仁甲牙关紧咬，眼里只有通往机舱的路。

俩人瞬间擦身而过，枪声不断在陆仁甲身后响起。

也就三秒，枪声没了，一颗头颅抛起，从天而降落在了陆仁甲前面，正是颜莉的脑袋。

陆仁甲来不及有什么反应，一根尖刺直接从他胸膛贯穿，扎穿了他的心脏。

“扑！”

血液喷洒，体力飞快流逝。

借着惯性，陆仁甲往前踉跄了几步，直接一头栽在了地上。

他眼皮十分沉重，在他旁边一米的位置，是颜莉没有瞑目的头颅。

【我这路人的一生....就这样没了吗？】

【呵...不过还不错...最后和女神死在一起了....】

思绪的最后，他闭上了眼睛，身边传来了脚步，应该就是那控心虫的。

在他身下，被洞穿的胸口，正紧紧的贴着生物引擎箱子，他的血液不断渗入箱子里面，一束淡淡的血色粒子，也从箱子进入了他的身体。

【基因序列匹配成功】

【生物引擎激活】

【注册代号】

【修罗】

下一秒，死去的陆仁甲睁开了双眼，瞳孔猩红，血色的气息自他全身弥漫。
【生物引擎连接中】

【基因序列匹配】

【基因融合中】

【人族列兵：陆仁甲】

【派系：指挥官】

【程序不兼容】

【程序更改中....】

未知的黑暗世界，陆仁甲感觉自己身处在严寒之中。

“好冷”

“尼玛，还没死透吗....”

【备用程序启动】

【未发现异族基因】

【权限通过】

【引擎融合开始】

陆仁甲什么都看不见，只能听到机械的合成音：

“张伟?被打出故障了吗?”

【10%】

【30%】

【50%】

“好吵啊......”

【100%】

【融合成功】

“什么融合成功...”

“等等?”

“难道是生物引擎?！！”

【基因匹配融合完成】

【恭喜你】

【列兵陆仁甲】

【生物引擎完全匹配】

【职业：指挥官】

【职业名称：修罗战神】

【区间错误，修改中】

【修改完成】

【职业更改：战士】

【注册代号：修罗】

瞬间，冰冷的感觉消失了，一股温暖的生机在他感受中慢慢萌芽。

随后，一股强烈的拖拽力将他思维拉回了身体。

再次睁眼，现实世界只过去了几秒，而他眼睛充满了血色，身体周围也布满了血气。

还没来得及感受什么，陆仁甲以违背力学的方式，如同悬浮一样站了起来。

随之而来的，还有无尽的头疼：

“啊啊啊！！！”

他双手抱着头，撕心裂肺的嘶吼着。

胸口的致命伤早就恢复如初，除了衣服破损，看不出有任何的伤疤。

“*#%@？”

诡异的音节从身边响起，陆仁甲转头，他的大脑直接被洞穿，连人一起钉在了墙上。

血液顺着控心虫的尖刺触手流淌，可明明是致命的伤，陆仁甲却感觉整个人舒适了不少。

这贯穿大脑的一击，让他的头疼削弱了很多。

紧接着，他瞳孔收缩，条件反射般一把抓住了还钉在他头上的触手，随后用力一扯，直接把触手扯断了。

他落在地上，抬手将刺入大脑的尖刺拔出，被穿透的地方开始飞速愈合，不到两秒就完好如初。

【修罗引擎，越是受伤，越是强大】

在接连两次致命伤后，陆仁甲也激发了凶性：

“好...疼...啊！！！”

他声音宛如几十年没有喝过水一样干哑，眼前的视野也被一层血色笼罩模糊。

下一秒，他手臂上浮现了猩红的血纹，一把朝着控心虫抓去。

野性在这一刻得到了释放，他活生生的把这具无头尸体撕开。

肉身里面早就被控心虫吃光，只剩一层皮肉，内部是如同蛞蝓一样软体的巨型虫身。

被撕开了外衣，控心虫身体飞快蠕动，无数触手延伸，随后尖端飞快硬化，把陆仁甲扎成了仙人掌。

控心虫，物理伤害无法造成致命伤，而修罗引擎又是物理系的。

出于战斗本能，陆仁甲做出了一个惊人的操作。

他无视身上的贯穿伤，直接扯下一坨布满粘液的肉塞嘴里咽下。

他确实没有化学类或者元素类的能力，但他有胃酸。

将这控心虫给吃掉，就是陆仁甲唯一的办法。

那一块肉进入陆仁甲体内以后瞬间化作独立个体，疯狂朝着陆仁甲的脑袋去钻，想要占据他的身体。

可它发现，自己根本用不上力了。

虫族那引以为傲的食欲，在此刻的修罗面前如同蝼蚁一样。

陆仁甲的胃酸直接上涌将它淹没，仅仅片刻就消化的一干二净。

一块，两块。

当陆仁甲吃完最后一块虫肉，身上衣服早已破破烂烂，不过肉身完好无损。

身上的血气消失了，猩红的双眼恢复正常。

他低头不语，走到了颜莉的头颅前，蹲在捧起，把她带到了倒地的尸体旁边。

此刻边陲星宛如地狱，天空上已经出现了虫族母舰，【泰坦巨虫】。

这一种虫子能够在太空生存，其内部的腔室更是如同太空堡垒的舱室一样。

虫族母皇降临，灭星开启了。

想要和一位虫族母皇战斗，至少也要有二十艘星际大舰的火力，不然虫族的兵力足以吞没一切。

【修罗之体，不死之躯】

生物引擎的信息断断续续的反馈到了陆仁甲脑海里面。

他看着身边颜莉的尸体，还有远处导师和同学的尸体，以及周围不断传来的虫鸣与枪声。

血瞳再现，血气缭绕。

“不死之躯吗.....”

弯腰捡起颜莉掉在地上的大狙背在身后，脑子里面回忆着上学时导师的话：

“宇宙浩瀚，物种无数，但有一个共通点，不管是人族，虫族，星族，兽族，都只有一条生命。”

“哪怕一些号称不死的族群，在用正确的方式去击杀时，也是如此。”

“找准敌方的弱点，一击便能毙命。”

虫族是蜂巢意识体，虫族母皇拥有宇宙最强的精神力，也是宇宙天生的指挥官。

可以说每一只虫子都是虫族母皇的手脚。

只要将虫族母皇击毙，那没了指挥的虫子，就能直接降低一半的威胁。

此刻，陆仁甲要做的，就是进行斩首行动。

大三的学生，刚好是所有理论知识都学完的时候。

从大四开始就要进入星际的战场进行实训。

本来再过两个月，陆仁甲就要进入军队实训了，现在只不过是提前了两月而已。

“张伟，带我去母巢。”

边上的工农一号悬浮而起，化作飞行背包落在他背上。

推进器启动，带着修罗直奔虫族母巢而去......

........

“说时迟那时快，我以无敌之姿横扫整个虫族母巢。”

“最后单手提着大狙指着虫族母皇的脑袋。”

“那母皇被我的霸气折服，直接吓尿了，当场跪地求饶。”

“我陆仁甲何许人也，宇宙修罗，直接一枪给它脑袋爆了。”

“至此，我成为了全宇宙唯一单刷虫族母巢的男人，成为了全宇宙崇拜的虫族克星！！”

大漠星系，天穹银河系太空，某星际旅游飞船上，陆仁甲一身佣兵打扮正在给眼前一位呆滞的小孩讲述自己的英雄事迹。

“萨拉星，即将到站，请乘客做好下船准备。”

听到广播，陆仁甲语速加快：

“现在，只要你给我3000星币，未来我成为宇宙霸主后，必定感谢你今天资助，给你一个星球当领主！”

小屁孩吸溜了一下鼻涕：

“骗子。”
“可恶，这个小鬼是怎么看破的！”

回到自己的座位，陆仁甲摸了摸带着胡蹅的下巴，百思不得其解。

旁边一个俏皮可爱的三眼紫发姑娘给了他三个白眼：

“星际小学生都知道工农1号是不具备飞行到太空高度的能力，而虫族的泰坦巨虫只会停留在太空，你下次编故事也好歹讲一点儿逻辑吧。”

陆仁甲拍了一下大腿：

“原来如此！！！下次我就编成我开着宇宙飞船去和母皇1v1单挑！”

他旁边一个灰色皮肤长着尖耳朵的清瘦青年吐槽着：

“但凡有一点儿知识的都知道，母皇是不可能跟你单挑的，虫族的特性就是虫海战术，你想要靠近虫族母皇，前提是得把它爆出来的虫子给杀光。”

“比起编故事，还是好好把重心放在工作上吧，不然没钱给飞船注能了。”

三眼女，灰皮男，陆仁甲。

三人是星际的冒险者，不过和那些伟大的冒险者不一样，他们三个基本上算是杂工，干着一些边缘的杂活。

三眼女叫露露，紫色的皮肤和头发还有那标志的三只眼，证明着她是暗星的人族。

暗星人，天生的狙击手，拥有极强的动态视力。

可露露是刺客，更是刺客里面最不入流的盗贼，专门负责干些小偷小摸的事情。

灰皮尖耳男，名为怒风，那标志性的灰色皮肤与尖耳证明着他是暗夜精灵一族，这个族群的精灵大多数都会选择刺客这个行业。

而他没有刺客的天份，而是成为了技术人员。

陆仁甲，地球人族，流浪者，是三人的老板。

这次前往萨拉星，是他们接到了一个活。

萨拉星，盛产萨拉合金，这种合金在星空里面都是排名前一千的高级货色。

萨拉星人男性皮肤黝黑粗糙，生长着四臂，萨拉星的锻造工艺也是极其出名的，当然，也只是在天穹银河。

这次他们三人接到的任务，是萨拉星一位著名的九级工程师发布的任务，因为他最近被星际海盗盯上了。

九级工程师，在宇宙都不算多见，这样的人不需要任何多余的设备，只要给他足够的时间，就能在一颗星球建立起文明。

因此，这类大佬也是宇宙海盗的常客，因为他们几乎都在异族的悬赏之上。

正常来说，陆仁甲这支队伍是没有资格接到这种级别的任务，不过这位九级工程师大佬豪气无比。

直接说来的冒险者上不封顶，他就是要招募无数冒险者组成军队，给那一伙海盗歼灭立威。

而常年混迹宇宙冒险者大厅的陆仁甲是个老油条，被稍微关照了一下，接了这个肥差。

毕竟真要打起来他们这种小体量的冒险队根本派不上用场，真正的作战主力是那些大佬。

自然，那些大佬冒险者拿到的酬金会更多，他们就是扮演一个在后面声援呐喊的小弟。

可即便如此，他们也能拿到每个人3万星币的酬劳，要是能杀敌，一个海盗就是二十万星币的奖励。

听到怒风提起任务，陆仁甲没有丝毫坐像的翘着二郎腿，伸出小拇指扣了扣鼻子：

“这还用说吗，到了地方，先住酒店，然后吃一顿好的，再舒舒服洗个澡，找个酒吧点两个漂亮的小姐姐潇洒一下，最后等那些大佬打完架拿钱走人。”

是的，陆仁甲就是抱着摆烂的心态来的，以至于他都没有开船来，怕浪费能源。

怒风闭眼，额头爆起青筋：

“你就不能对待工作认真一点吗？！好歹看一看对手是谁啊！真以为三万没个人这么好拿吗？！”

露露探过头来：

“谁啊谁啊，很牛吗？”

怒风抬起臂甲，投影了一个全息图像：

“对方可是整个大漠星系赫赫有名的暗夜星际海盗团啊！”

陆仁甲和露露表情突变：

“什么！”

“居然是暗夜海盗团！！”

下一秒，俩人同时抠着鼻孔：

“谁啊？”

怒风怒了：

“不知道你们俩震惊个什么啊！！”

露露摊手：

“不是你说很厉害吗？这不是要给点反应嘛，是吧老大。”

陆仁甲一副孺子可教的表情拍了拍露露的脑袋：

“没错，做的很好露露，你长大了，我们做服务行业的，只需要给顾客提供优良的情绪价值就行了。”

露露竖起大拇指：

“必须的！”

怒风拳头捏紧了：

“对方可是拥有十六次灭星战绩的星系级海盗，是超越了银河级海盗的超大犯罪组织！”

“这次任务必然十分凶险，他们装备着歼星炮！要是处理不好，萨拉星都要被灭掉！”

听到这里，陆仁甲坐直了一些：

“事情大条了，歼星炮这玩意儿可不敢乱来了。”

怒风看到这反应，才稳定了下来：

“总之这次的任务我们必须打起精神，如果万不得已....”

陆仁甲此刻却在和露露说着：

“一会儿到了地方后，他们肯定会摆宴席，到时候把那些吃的全都打包装起来，晚上我们就跑路，3万块不要了，但难得来一趟，吃的要带走。”

露露一副认真的点头：

“嗯嗯！偷东西我最拿手了！”

怒风再次暴怒：

“你俩节操呢！！！”

十分钟后.....

飞船降落在了萨拉星星际港，三人穿着黑色作战服走了下来，每个人脸上都带着“冷酷”的表情，这种表情给外人一看就是十分可靠的类型。

陆仁甲走在中间，微微凌乱的头发，带着沧桑感的胡茬，忧郁的眼神，双手插在兜里一副不知道什么叫作对手的傲天模样。

露露一身黑色紧身衣，三眼配合紫发紫肤，以及腰间的两把匕首，俨然一副冷血杀手的状态。

而怒风一身黑色大衣，浅灰色的中长发梳着背头，暗夜精灵族的冷傲被他诠释得淋漓尽致。

三人一出场，就是众人的焦点，完美诠释了什么叫做【专业团队】。

而事实上.....

“这三人神经病吧？”

“耐热的族群?”

“今天这里40多度呢...”

“快走快走......”
.......
    """

    # 1. 调用 DeepSeek 提取关系
    raw_json = extract_relations(sample_text)
    try:
        data = json.loads(raw_json)
    except Exception:
        import traceback
        traceback.print_exc()
        print("⚠️ DeepSeek 输出不是合法 JSON，需要人工检查：")
        print(raw_json)
        exit()

    # 2. 存入 Neo4j
    insert_to_neo4j(data)

    # 3. 从 Neo4j 查询
    graph_data = get_graph_from_neo4j()

    # 4. 生成 HTML 展示
    generate_html(graph_data)
