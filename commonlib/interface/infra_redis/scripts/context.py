from typing import Any, Dict

SCRIPT_ATOMIC_COUNTER = "atomic_counter"
SCRIPT_SAFE_DELETE = "safe_delete"
SCRIPT_CACHE_SET_NX = "cache_set_nx"
SCRIPT_TOKEN_BUCKET = "token_bucket"
# 预定义的 Redis Lua 脚本集
DEFAULT_SCRIPTS: Dict[str, Dict[str, Any]] = {
    SCRIPT_ATOMIC_COUNTER: {
        "version": "1.0",
        "content": """
            local key = KEYS[1]
            local value = tonumber(ARGV[1])
            return redis.call('INCRBY', key, value)
        """,
    },
    SCRIPT_SAFE_DELETE: {
        "version": "1.0",
        "content": """
            if redis.call('GET', KEYS[1]) == ARGV[1] then
                return redis.call('DEL', KEYS[1])
            end
            return 0
        """,
    },
    SCRIPT_CACHE_SET_NX: {
        "version": "1.0",
        "content": """
            -- KEYS[1]: 缓存key
            -- ARGV[1]: 缓存值
            -- ARGV[2]: TTL(秒)
            -- return: 1成功 0失败
            local exists = redis.call('EXISTS', KEYS[1])
            if exists == 0 then
                redis.call('SET', KEYS[1], ARGV[1])
                if tonumber(ARGV[2]) > 0 then
                    redis.call('EXPIRE', KEYS[1], ARGV[2])
                end
                return 1
            end
            return 0
        """,
    },
    SCRIPT_TOKEN_BUCKET: {
        "version": "1.0",
        "content": """
            local key = KEYS[1]
            local now = tonumber(ARGV[1])
            local capacity = tonumber(ARGV[2])
            local fill_rate = tonumber(ARGV[3])
            local requested = tonumber(ARGV[4])
            local timeout = tonumber(ARGV[5])

            local bucket = redis.call('HMGET', key, 'tokens', 'last_time')
            local tokens = tonumber(bucket[1]) or capacity
            local last_time = tonumber(bucket[2]) or now

            -- 计算新增令牌
            local delta = math.max(0, now - last_time)
            tokens = math.min(capacity, tokens + delta * fill_rate)

            -- 检查是否有足够令牌
            if tokens >= requested then
                tokens = tokens - requested
                redis.call('HMSET', key, 'tokens', tokens, 'last_time', now)
                redis.call('EXPIRE', key, math.ceil(capacity / fill_rate) + 1)
                return 1  -- 成功
            end

            -- 计算需要等待的时间
            local wait_time = (requested - tokens) / fill_rate
            if wait_time <= timeout then
                return 2  -- 需要等待
            end

            return 0  -- 超过最大等待时间
        """,
    },
}
