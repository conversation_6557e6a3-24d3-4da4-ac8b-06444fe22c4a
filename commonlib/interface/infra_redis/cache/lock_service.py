import asyncio
import uuid
from typing import Optional

from commonlib.storages.persistence.redis.repository import RedisRepository


class RedisLockService:
    """极简分布式锁 (无自动续期)"""

    def __init__(self, redis_repo: RedisRepository, default_ttl: int = 30):
        """
        :param redis_repo: Redis连接实例 (需支持async)
        :param default_ttl: 默认锁过期时间(秒)
        """
        self.redis_repo = redis_repo
        self.default_ttl = default_ttl

    async def acquire(self, key: str, ttl: Optional[int] = None) -> Optional[str]:
        """
        尝试获取锁 (非阻塞)
        :param key: 资源标识
        :param ttl: 自定义过期时间(秒)
        :return: 成功返回lock_id，失败返回None
        """
        lock_id = str(uuid.uuid4())
        ttl = ttl or self.default_ttl

        success = await self.redis_repo.set(key, lock_id, ex=ttl, nx=True)  # 仅当key不存在时设置
        return lock_id if success else None

    async def release(self, key: str, lock_id: str) -> bool:
        """
        释放锁 (原子操作)
        :return: 是否释放成功
        """
        lua_script = """
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
        """
        return bool(await self.redis_repo.client.eval(lua_script, 1, key, lock_id))

    async def try_lock(
        self, key: str, ttl: Optional[int] = None, timeout: Optional[float] = None
    ) -> Optional[str]:
        """
        带超时的锁获取
        :param ttl:
        :param key:
        :param timeout: 最大等待时间(秒)
        :return: lock_id 或 None
        """
        deadline = asyncio.get_event_loop().time() + (timeout or 0)

        while True:
            if lock_id := await self.acquire(key, ttl):
                return lock_id

            if timeout and asyncio.get_event_loop().time() >= deadline:
                return None

            await asyncio.sleep(0.1)  # 避免CPU空转
